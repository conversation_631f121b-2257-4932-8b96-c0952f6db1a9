#include "rectangletool.h"
#include "../items/rectitem.h"
#include <QDebug>

RectangleTool::RectangleTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Rectangle, parent)
    , m_currentRect(nullptr)
{
}

RectangleTool::~RectangleTool()
{
    finishCurrentOperation();
}

void RectangleTool::onInputPress(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    m_isDrawing = true;
    m_startPoint = scenePos;

    // 创建新的矩形项
    m_currentRect = createNewRectangle(m_startPoint);
}

void RectangleTool::onInputMove(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    if (!m_isDrawing || !m_currentRect) {
        return;
    }

    // 直接更新矩形
    QRectF rect(m_startPoint, scenePos);
    m_currentRect->setRect(rect.normalized());
}

void RectangleTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)
    Q_UNUSED(scenePos)

    if (m_isDrawing && m_currentRect) {
        finishRectangle();
    }

    m_isDrawing = false;
    m_currentRect = nullptr;
}

void RectangleTool::finishCurrentOperation()
{
    if (m_currentRect) {
        finishRectangle();
        m_currentRect = nullptr;
    }

    m_isDrawing = false;
    BaseTool::finishCurrentOperation();
}

void RectangleTool::cancelCurrentOperation()
{
    if (m_currentRect) {
        removeItemFromScene(m_currentRect);
        m_currentRect = nullptr;
    }

    m_isDrawing = false;
    BaseTool::cancelCurrentOperation();
}

RectItem* RectangleTool::createNewRectangle(const QPointF &startPoint)
{
    RectItem *rectItem = new RectItem();

    QRectF initialRect(startPoint, QSizeF(0, 0));
    rectItem->setRect(initialRect);

    addItemToScene(rectItem);

    return rectItem;
}

void RectangleTool::finishRectangle()
{
    if (m_currentRect) {
        emit itemFinished(m_currentRect);
    }
}


