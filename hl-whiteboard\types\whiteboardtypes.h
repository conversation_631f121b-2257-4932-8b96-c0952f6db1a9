#ifndef WHITEBOARDTYPES_H
#define WHITEBOARDTYPES_H

#include <QString>
#include <QObject>
#include <QMetaType>
#include <QColor>
#include <QPointF>
#include <QDateTime>
#include <QTransform>

// 统一的类型定义和命名空间
namespace WhiteboardTypes {
    // 绘图工具类型枚举
    enum class DrawingType {
        Pen,           // 画笔工具
        Eraser,        // 橡皮擦工具
        Rectangle,     // 矩形工具
        Ellipse,       // 椭圆工具
        Line,          // 直线工具
        Lasso,         // 套索选择工具
        Group          // 组合项
    };
    
    // 类型转换辅助函数
    QString drawingTypeToString(DrawingType type);
    DrawingType stringToDrawingType(const QString &str);
    
    // 类型安全的辅助函数
    bool isValidDrawingType(int typeValue);
    bool isShapeType(DrawingType type);
    bool supportsMultiTouch(DrawingType type);

    // 输入ID常量
    static const int MOUSE_INPUT_ID = -1;           // 鼠标输入的固定ID

    // 事件类型枚举
    enum class EventType {
        Press,      // 按下事件
        Move,       // 移动事件
        Release     // 释放事件
    };

    // 输入源类型枚举
    enum class InputSource {
        Mouse,      // 鼠标输入
        Touch,      // 触摸输入
        Pen         // 手写笔输入
    };

    // 标准化的绘图事件结构
    struct DrawingEvent {
        int inputId;                    // 输入ID (鼠标为-1，触摸为触摸点ID)
        QPointF scenePosition;          // 场景坐标位置
        EventType type;                 // 事件类型
        InputSource source;             // 输入源
        QDateTime timestamp;            // 事件时间戳

        DrawingEvent() : inputId(MOUSE_INPUT_ID), type(EventType::Press), source(InputSource::Mouse) {}
        DrawingEvent(int id, const QPointF &pos, EventType t, InputSource s)
            : inputId(id), scenePosition(pos), type(t), source(s), timestamp(QDateTime::currentDateTime()) {}
    };

    // 场景事件结构
    struct SceneEvent {
        QPointF scenePosition;          // 场景坐标位置
        EventType type;                 // 事件类型
        QDateTime timestamp;            // 事件时间戳

        SceneEvent() : type(EventType::Press) {}
        SceneEvent(const QPointF &pos, EventType t)
            : scenePosition(pos), type(t), timestamp(QDateTime::currentDateTime()) {}
    };

    // 视图事件结构
    struct ViewEvent {
        QTransform transform;           // 视图变换
        QPointF viewPosition;           // 视图坐标位置
        EventType type;                 // 事件类型
        QDateTime timestamp;            // 事件时间戳

        ViewEvent() : type(EventType::Press) {}
        ViewEvent(const QTransform &t, const QPointF &pos, EventType et)
            : transform(t), viewPosition(pos), type(et), timestamp(QDateTime::currentDateTime()) {}
    };
}

// 注册自定义类型到 Qt 元对象系统
Q_DECLARE_METATYPE(WhiteboardTypes::DrawingEvent)
Q_DECLARE_METATYPE(WhiteboardTypes::SceneEvent)
Q_DECLARE_METATYPE(WhiteboardTypes::ViewEvent)
Q_DECLARE_METATYPE(WhiteboardTypes::EventType)
Q_DECLARE_METATYPE(WhiteboardTypes::InputSource)

#endif // WHITEBOARDTYPES_H