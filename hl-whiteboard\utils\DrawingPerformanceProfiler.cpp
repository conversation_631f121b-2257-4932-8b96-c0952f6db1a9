#include "DrawingPerformanceProfiler.h"
#include <QMutexLocker>
#include <QThread>

DrawingPerformanceProfiler& DrawingPerformanceProfiler::instance()
{
    static DrawingPerformanceProfiler instance;
    return instance;
}

void DrawingPerformanceProfiler::setEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_enabled = enabled;
    
    if (enabled) {
        qDebug() << "[绘制性能] 性能检测已启用";
    } else {
        qDebug() << "[绘制性能] 性能检测已禁用";
        // 清理活动计时器
        m_activeTimers.clear();
    }
}

bool DrawingPerformanceProfiler::isEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_enabled;
}

int DrawingPerformanceProfiler::startTiming(const QString& name)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_enabled) {
        return -1;
    }

    int timerId = m_nextTimerId++;
    TimingInfo& info = m_activeTimers[timerId];
    info.name = name;
    info.timer.start();

    return timerId;
}

void DrawingPerformanceProfiler::endTiming(int timerId)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_enabled || timerId < 0) {
        return;
    }

    auto it = m_activeTimers.find(timerId);
    if (it == m_activeTimers.end()) {
        return;
    }

    const TimingInfo& info = it.value();
    qint64 elapsedTime = info.timer.elapsed();
    
    // 输出计时日志
    qDebug() << QString("[绘制性能] %1 耗时: %2ms").arg(info.name).arg(elapsedTime);
    
    // 更新统计信息
    updateStatistics(info.name, elapsedTime);
    
    // 移除活动计时器
    m_activeTimers.erase(it);
}

void DrawingPerformanceProfiler::reset()
{
    QMutexLocker locker(&m_mutex);
    m_activeTimers.clear();
    m_statistics.clear();
    m_nextTimerId = 1;
    qDebug() << "[绘制性能] 统计数据已重置";
}

void DrawingPerformanceProfiler::printSummary()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_statistics.isEmpty()) {
        qDebug() << "[绘制性能] 暂无统计数据";
        return;
    }

    qDebug() << "[绘制性能] ========== 性能统计摘要 ==========";
    
    for (auto it = m_statistics.begin(); it != m_statistics.end(); ++it) {
        const QString& name = it.key();
        const StatInfo& stat = it.value();
        
        qDebug() << QString("[绘制性能] %1:").arg(name);
        qDebug() << QString("  调用次数: %1").arg(stat.count);
        qDebug() << QString("  总耗时: %1ms").arg(stat.totalTime);
        qDebug() << QString("  平均耗时: %1ms").arg(stat.avgTime);
        qDebug() << QString("  最小耗时: %1ms").arg(stat.minTime);
        qDebug() << QString("  最大耗时: %1ms").arg(stat.maxTime);
    }
    
    qDebug() << "[绘制性能] =====================================";
}

void DrawingPerformanceProfiler::updateStatistics(const QString& name, qint64 elapsedTime)
{
    StatInfo& stat = m_statistics[name];
    stat.count++;
    stat.totalTime += elapsedTime;
    stat.minTime = qMin(stat.minTime, elapsedTime);
    stat.maxTime = qMax(stat.maxTime, elapsedTime);
    stat.avgTime = stat.totalTime / stat.count;
}

// DrawingTimerScope 实现
DrawingTimerScope::DrawingTimerScope(const QString& name)
    : m_timerId(-1)
    , m_valid(false)
{
    if (DrawingPerformanceProfiler::instance().isEnabled()) {
        m_timerId = DrawingPerformanceProfiler::instance().startTiming(name);
        m_valid = (m_timerId >= 0);
    }
}

DrawingTimerScope::~DrawingTimerScope()
{
    if (m_valid) {
        DrawingPerformanceProfiler::instance().endTiming(m_timerId);
    }
}
