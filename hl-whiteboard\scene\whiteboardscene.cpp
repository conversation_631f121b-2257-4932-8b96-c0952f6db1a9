#include "whiteboardscene.h"
#include "scenemanager.h"
#include "../coordination/eventcoordinator.h"
#include <QGraphicsSceneMouseEvent>
#include <QGraphicsSceneContextMenuEvent>
#include <QDebug>

WhiteboardScene::WhiteboardScene(QObject *parent)
    : QGraphicsScene(parent)
    , m_backgroundColor(Qt::white)
    , m_sceneManager(nullptr)
    , m_eventCoordinator(nullptr)
{
    setupScene();
}

WhiteboardScene::WhiteboardScene(const QRectF &sceneRect, QObject *parent)
    : QGraphicsScene(sceneRect, parent)
    , m_backgroundColor(Qt::white)
    , m_sceneManager(nullptr)
    , m_eventCoordinator(nullptr)
{
    setupScene();
}

WhiteboardScene::~WhiteboardScene()
{
    // 基础清理，高级功能由SceneManager负责
}

void WhiteboardScene::setupScene()
{
    // 设置场景背景
    setBackgroundBrush(QBrush(m_backgroundColor));
}

// 管理器设置实现
void WhiteboardScene::setSceneManager(SceneManager *manager)
{
    m_sceneManager = manager;
}

SceneManager* WhiteboardScene::sceneManager() const
{
    return m_sceneManager;
}

void WhiteboardScene::setEventCoordinator(EventCoordinator *coordinator)
{
    m_eventCoordinator = coordinator;
}

EventCoordinator* WhiteboardScene::eventCoordinator() const
{
    return m_eventCoordinator;
}

// 基础场景属性实现
void WhiteboardScene::setBackgroundColor(const QColor &color)
{
    if (m_backgroundColor == color) return;

    m_backgroundColor = color;
    setBackgroundBrush(QBrush(color));
}

QColor WhiteboardScene::backgroundColor() const
{
    return m_backgroundColor;
}

// 统一的事件处理辅助方法
void WhiteboardScene::handleMouseEvent(QGraphicsSceneMouseEvent *event, const QString &eventType)
{
    // 发出对应的信号供SceneManager监听
    if (eventType == "Press") {
        emit sceneMousePress(event);
    } else if (eventType == "Move") {
        emit sceneMouseMove(event);
    } else if (eventType == "Release") {
        emit sceneMouseRelease(event);
    } else if (eventType == "DoubleClick") {
        emit sceneMouseDoubleClick(event);
    }

    // 转发事件给EventCoordinator
    if (m_eventCoordinator) {
        if (eventType == "Press") {
            m_eventCoordinator->handleMousePress(event);
        } else if (eventType == "Move") {
            m_eventCoordinator->handleMouseMove(event);
        } else if (eventType == "Release") {
            m_eventCoordinator->handleMouseRelease(event);
        }
        return;
    }

    // 如果没有 EventCoordinator，回退到标准 Qt 行为
    if (eventType == "Press") {
        QGraphicsScene::mousePressEvent(event);
    } else if (eventType == "Move") {
        QGraphicsScene::mouseMoveEvent(event);
    } else if (eventType == "Release") {
        QGraphicsScene::mouseReleaseEvent(event);
    } else if (eventType == "DoubleClick") {
        QGraphicsScene::mouseDoubleClickEvent(event);
    }
}

// 简化的事件处理方法
void WhiteboardScene::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    handleMouseEvent(event, "Press");
}

void WhiteboardScene::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    handleMouseEvent(event, "Move");
}

void WhiteboardScene::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    handleMouseEvent(event, "Release");
}

void WhiteboardScene::mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    // 发出信号供SceneManager监听
    emit sceneMouseDoubleClick(event);
    
    // 调用父类处理默认行为
    QGraphicsScene::mouseDoubleClickEvent(event);
}

void WhiteboardScene::contextMenuEvent(QGraphicsSceneContextMenuEvent *event)
{
    // 简单转发给父类处理
    QGraphicsScene::contextMenuEvent(event);
}

// 触摸事件处理 - 统一事件路径
void WhiteboardScene::handleTouchPress(int touchId, const QPointF &scenePos)
{
    // 发出信号供SceneManager监听（与鼠标事件保持一致）
    // 创建一个虚拟的鼠标事件来触发信号
    QGraphicsSceneMouseEvent mouseEvent(QEvent::GraphicsSceneMousePress);
    mouseEvent.setScenePos(scenePos);
    mouseEvent.setButton(Qt::LeftButton);
    emit sceneMousePress(&mouseEvent);

    // 转发给EventCoordinator
    if (m_eventCoordinator) {
        m_eventCoordinator->handleTouchPress(touchId, scenePos);
    }
}

void WhiteboardScene::handleTouchMove(int touchId, const QPointF &scenePos)
{
    // 发出信号供SceneManager监听
    QGraphicsSceneMouseEvent mouseEvent(QEvent::GraphicsSceneMouseMove);
    mouseEvent.setScenePos(scenePos);
    mouseEvent.setButton(Qt::LeftButton);
    emit sceneMouseMove(&mouseEvent);

    // 转发给EventCoordinator
    if (m_eventCoordinator) {
        m_eventCoordinator->handleTouchMove(touchId, scenePos);
    }
}

void WhiteboardScene::handleTouchRelease(int touchId, const QPointF &scenePos)
{
    // 发出信号供SceneManager监听
    QGraphicsSceneMouseEvent mouseEvent(QEvent::GraphicsSceneMouseRelease);
    mouseEvent.setScenePos(scenePos);
    mouseEvent.setButton(Qt::LeftButton);
    emit sceneMouseRelease(&mouseEvent);

    // 转发给EventCoordinator
    if (m_eventCoordinator) {
        m_eventCoordinator->handleTouchRelease(touchId, scenePos);
    }
}



