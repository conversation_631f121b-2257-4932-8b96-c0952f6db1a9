#include "eventcoordinator.h"
#include "../tools/toolmanager.h"
#include "../scene/scenemanager.h"
#include "../types/whiteboardtypes.h"
#include <QDebug>

EventCoordinator::EventCoordinator(QObject *parent)
    : QObject(parent)
    , m_toolManager(nullptr)
    , m_sceneManager(nullptr)
    , m_eventMode(EventMode::Drawing)
    , m_multiTouchEnabled(false)
{
}

EventCoordinator::~EventCoordinator()
{
}

void EventCoordinator::setToolManager(ToolManager *toolManager)
{
    if (m_toolManager != toolManager) {
        if (m_toolManager) {
            disconnect(m_toolManager, nullptr, this, nullptr);
        }
        
        m_toolManager = toolManager;
        
        if (m_toolManager) {
            connect(m_toolManager, &ToolManager::toolChanged, 
                    this, &EventCoordinator::onToolChanged);
        }
    }
}

void EventCoordinator::setSceneManager(SceneManager *sceneManager)
{
    m_sceneManager = sceneManager;
}

void EventCoordinator::setEventMode(EventMode mode)
{
    if (m_eventMode != mode) {
        m_eventMode = mode;
        emit eventModeChanged(mode);
        qDebug() << "EventCoordinator mode changed to:" << static_cast<int>(mode);
    }
}

EventCoordinator::EventMode EventCoordinator::eventMode() const
{
    return m_eventMode;
}

// 统一的输入事件处理接口
void EventCoordinator::handleInputPress(int inputId, const QPointF &scenePos, bool isTouch)
{
    // 根据当前模式分发事件
    if (m_eventMode == EventMode::Drawing) {
        dispatchToToolManager(inputId, scenePos, isTouch, "Press");
    } else if (m_eventMode == EventMode::Selection) {
        dispatchToSceneManager(inputId, scenePos, isTouch, "Press");
    }
}

void EventCoordinator::handleInputMove(int inputId, const QPointF &scenePos, bool isTouch)
{
    // 根据当前模式分发事件
    if (m_eventMode == EventMode::Drawing) {
        dispatchToToolManager(inputId, scenePos, isTouch, "Move");
    } else if (m_eventMode == EventMode::Selection) {
        dispatchToSceneManager(inputId, scenePos, isTouch, "Move");
    }
}

void EventCoordinator::handleInputRelease(int inputId, const QPointF &scenePos, bool isTouch)
{
    // 根据当前模式分发事件
    if (m_eventMode == EventMode::Drawing) {
        dispatchToToolManager(inputId, scenePos, isTouch, "Release");
    } else if (m_eventMode == EventMode::Selection) {
        dispatchToSceneManager(inputId, scenePos, isTouch, "Release");
    }
}

// 便捷的鼠标事件接口
void EventCoordinator::handleMousePress(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        handleInputPress(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
    }
}

void EventCoordinator::handleMouseMove(QGraphicsSceneMouseEvent *event)
{
    handleInputMove(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
}

void EventCoordinator::handleMouseRelease(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        handleInputRelease(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
    }
}

// 触摸事件处理
void EventCoordinator::handleTouchPress(int touchId, const QPointF &scenePos)
{
    handleInputPress(touchId, scenePos, true);
}

void EventCoordinator::handleTouchMove(int touchId, const QPointF &scenePos)
{
    handleInputMove(touchId, scenePos, true);
}

void EventCoordinator::handleTouchRelease(int touchId, const QPointF &scenePos)
{
    handleInputRelease(touchId, scenePos, true);
}



// 私有方法实现
void EventCoordinator::dispatchToToolManager(int inputId, const QPointF &scenePos, bool isTouch, const QString &eventType)
{
    if (!m_toolManager) {
        qDebug() << "No ToolManager available for event dispatch:" << eventType;
        return;
    }
    
    if (eventType == "Press") {
        m_toolManager->handleInputPress(inputId, scenePos, isTouch);
    } else if (eventType == "Move") {
        m_toolManager->handleInputMove(inputId, scenePos, isTouch);
    } else if (eventType == "Release") {
        m_toolManager->handleInputRelease(inputId, scenePos, isTouch);
    }
}

void EventCoordinator::dispatchToSceneManager(int inputId, const QPointF &scenePos, bool isTouch, const QString &eventType)
{
    if (!m_sceneManager) {
        qDebug() << "No SceneManager available for event dispatch:" << eventType;
        return;
    }
    
    // TODO: SceneManager 需要添加类似的输入事件处理接口
    qDebug() << "Dispatching to SceneManager:" << eventType << "at" << scenePos;
}



void EventCoordinator::onToolChanged()
{
    // 工具切换时，自动切换到绘图模式
    if (m_eventMode != EventMode::Drawing) {
        setEventMode(EventMode::Drawing);
    }
}

// 多点触控管理实现
void EventCoordinator::setMultiTouchEnabled(bool enabled)
{
    if (m_multiTouchEnabled != enabled) {
        m_multiTouchEnabled = enabled;
        qDebug() << "EventCoordinator multi-touch:" << (enabled ? "enabled" : "disabled");
    }
}

bool EventCoordinator::multiTouchEnabled() const
{
    return m_multiTouchEnabled;
}
