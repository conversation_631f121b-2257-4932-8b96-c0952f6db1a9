#include "whiteboardtypes.h"

// WhiteboardTypes 命名空间函数实现
namespace WhiteboardTypes {

QString drawingTypeToString(DrawingType type)
{
    switch (type) {
    case DrawingType::Pen:
        return QStringLiteral("Pen");
    case DrawingType::Eraser:
        return QStringLiteral("Eraser");
    case DrawingType::Rectangle:
        return QStringLiteral("Rectangle");
    case DrawingType::Ellipse:
        return QStringLiteral("Ellipse");
    case DrawingType::Line:
        return QStringLiteral("Line");
    case DrawingType::Lasso:
        return QStringLiteral("Lasso");
    case DrawingType::Group:
        return QStringLiteral("Group");
    default:
        return QStringLiteral("Unknown");
    }
}

DrawingType stringToDrawingType(const QString &str)
{
    if (str == QStringLiteral("Pen"))
        return DrawingType::Pen;
    else if (str == QStringLiteral("Eraser"))
        return DrawingType::Eraser;
    else if (str == QStringLiteral("Rectangle"))
        return DrawingType::Rectangle;
    else if (str == QStringLiteral("Ellipse"))
        return DrawingType::Ellipse;
    else if (str == QStringLiteral("Line"))
        return DrawingType::Line;
    else if (str == QStringLiteral("Lasso"))
        return DrawingType::Lasso;
    else if (str == QStringLiteral("Group"))
        return DrawingType::Group;
    else
        return DrawingType::Pen; // 默认返回画笔工具
}

bool isValidDrawingType(int typeValue)
{
    return typeValue >= static_cast<int>(DrawingType::Pen) && 
           typeValue <= static_cast<int>(DrawingType::Group);
}

bool isShapeType(DrawingType type)
{
    return type == DrawingType::Rectangle || 
           type == DrawingType::Ellipse || 
           type == DrawingType::Line;
}

bool supportsMultiTouch(DrawingType type)
{
    // 目前只有画笔工具支持多指绘制
    return type == DrawingType::Pen;
}

} // namespace WhiteboardTypes