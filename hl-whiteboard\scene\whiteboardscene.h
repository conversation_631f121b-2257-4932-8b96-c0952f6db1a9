#ifndef WHITEBOARDSCENE_H
#define WHITEBOARDSCENE_H

#include <QGraphicsScene>
#include "../types/whiteboardtypes.h"

class DrawingItemBase;
class QGraphicsItem;
class SceneManager;
class EventCoordinator;

/**
 * @brief WhiteboardScene - 轻量级QGraphicsScene实现
 *
 * 专注于核心Scene功能：
 * - 事件处理和坐标转换
 * - 事件转发给EventCoordinator
 * - 基础场景属性管理
 *
 * 高级功能由SceneManager负责：
 * - 图形项生命周期管理
 * - 无限扩展场景支持
 * - 状态管理和协调机制
 */
class WhiteboardScene : public QGraphicsScene
{
    Q_OBJECT

public:
    explicit WhiteboardScene(QObject *parent = nullptr);
    explicit WhiteboardScene(const QRectF &sceneRect, QObject *parent = nullptr);
    ~WhiteboardScene();

    // 管理器设置
    void setSceneManager(SceneManager *manager);
    SceneManager* sceneManager() const;

    void setEventCoordinator(EventCoordinator *coordinator);
    EventCoordinator* eventCoordinator() const;

    // 触摸事件处理（供WhiteboardView调用，统一事件路径）
    void handleTouchPress(int touchId, const QPointF &scenePos);
    void handleTouchMove(int touchId, const QPointF &scenePos);
    void handleTouchRelease(int touchId, const QPointF &scenePos);

    // 基础场景属性
    void setBackgroundColor(const QColor &color);
    QColor backgroundColor() const;



signals:
    // 基础事件信号 - 供SceneManager监听
    void sceneMousePress(QGraphicsSceneMouseEvent *event);
    void sceneMouseMove(QGraphicsSceneMouseEvent *event);
    void sceneMouseRelease(QGraphicsSceneMouseEvent *event);
    void sceneMouseDoubleClick(QGraphicsSceneMouseEvent *event);

protected:
    // 统一的鼠标事件处理
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event) override;
    void contextMenuEvent(QGraphicsSceneContextMenuEvent *event) override;

private:
    void setupScene();

    // 统一的事件处理辅助方法
    void handleMouseEvent(QGraphicsSceneMouseEvent *event, const QString &eventType);

    // 私有成员变量
    QColor m_backgroundColor;

    // 管理器引用
    SceneManager *m_sceneManager;
    EventCoordinator *m_eventCoordinator;
    
    Q_DISABLE_COPY(WhiteboardScene)
};

#endif // WHITEBOARDSCENE_H