#ifndef EVENTCOORDINATOR_H
#define EVENTCOORDINATOR_H

#include <QObject>
#include <QGraphicsSceneMouseEvent>
#include <QSet>
#include "../types/whiteboardtypes.h"

class ToolManager;
class SceneManager;

/**
 * @brief EventCoordinator - 事件协调器
 *
 * 负责统一接收和分发输入事件，避免在多个类中重复处理相同事件。
 * 参考 ToolManager 的简洁设计，提供统一的事件分发机制。
 */
class EventCoordinator : public QObject
{
    Q_OBJECT

public:
    explicit EventCoordinator(QObject *parent = nullptr);
    ~EventCoordinator();

    // 组件设置
    void setToolManager(ToolManager *toolManager);
    void setSceneManager(SceneManager *sceneManager);

    // 事件处理模式
    enum class EventMode {
        Drawing,        // 绘图模式 - 事件转发给 ToolManager
        Selection       // 选择模式 - 事件转发给 SceneManager
    };

    void setEventMode(EventMode mode);
    EventMode eventMode() const;

    // 统一的输入事件处理接口（参考 ToolManager 设计）
    void handleInputPress(int inputId, const QPointF &scenePos, bool isTouch = false);
    void handleInputMove(int inputId, const QPointF &scenePos, bool isTouch = false);
    void handleInputRelease(int inputId, const QPointF &scenePos, bool isTouch = false);

    // 便捷的鼠标事件接口（直接处理 QGraphicsSceneMouseEvent）
    void handleMousePress(QGraphicsSceneMouseEvent *event);
    void handleMouseMove(QGraphicsSceneMouseEvent *event);
    void handleMouseRelease(QGraphicsSceneMouseEvent *event);

    // 触摸事件处理（供 WhiteboardScene 调用）
    void handleTouchPress(int touchId, const QPointF &scenePos);
    void handleTouchMove(int touchId, const QPointF &scenePos);
    void handleTouchRelease(int touchId, const QPointF &scenePos);

    // 多点触控管理
    void setMultiTouchEnabled(bool enabled);
    bool multiTouchEnabled() const;

signals:
    // 模式变更信号
    void eventModeChanged(EventMode mode);

private slots:
    void onToolChanged();

private:
    // 事件分发方法
    void dispatchToToolManager(int inputId, const QPointF &scenePos, bool isTouch, const QString &eventType);
    void dispatchToSceneManager(int inputId, const QPointF &scenePos, bool isTouch, const QString &eventType);

    // 私有成员变量
    ToolManager *m_toolManager;
    SceneManager *m_sceneManager;

    EventMode m_eventMode;
    bool m_multiTouchEnabled;

    Q_DISABLE_COPY(EventCoordinator)
};

#endif // EVENTCOORDINATOR_H
