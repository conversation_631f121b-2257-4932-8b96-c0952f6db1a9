#ifndef BASETOOL_H
#define BASETOOL_H

#include <QObject>
#include <QColor>
#include <QPen>
#include <QBrush>
#include <QGraphicsSceneMouseEvent>
#include <QMap>
#include <QVariant>
#include "../../types/whiteboardtypes.h"

// 前向声明
class QGraphicsScene;
class QGraphicsItem;

/**
 * @brief 所有绘图工具的基类
 * 
 * 定义了绘图工具的标准接口，包括鼠标事件处理、触摸事件处理、
 * 样式设置等功能。所有具体的工具类都应该继承此基类。
 */
class BaseTool : public QObject
{
    Q_OBJECT

public:
    explicit BaseTool(WhiteboardTypes::DrawingType type, QObject *parent = nullptr);
    virtual ~BaseTool();


    // 工具类型
    WhiteboardTypes::DrawingType toolType() const;

    // 统一的输入事件接口（纯虚函数，子类必须实现）
    virtual void onInputPress(int inputId, const QPointF &scenePos) = 0;
    virtual void onInputMove(int inputId, const QPointF &scenePos) = 0;
    virtual void onInputRelease(int inputId, const QPointF &scenePos) = 0;

    // 工具操作控制
    virtual void finishCurrentOperation();
    virtual void cancelCurrentOperation();

    // 场景设置和获取
    void setScene(QGraphicsScene *scene);
    QGraphicsScene* scene() const;

    // 状态查询
    bool isDrawing() const;

signals:
    void itemCreated(QGraphicsItem *item);
    void itemFinished(QGraphicsItem *item);

protected:
    // 图形项创建辅助
    void addItemToScene(QGraphicsItem *item);
    void removeItemFromScene(QGraphicsItem *item);

    // 从ToolManager获取当前样式设置
    QPen getCurrentPen() const;
    QBrush getCurrentBrush() const;
    QColor getCurrentPenColor() const;
    qreal getCurrentPenWidth() const;
    QColor getCurrentBrushColor() const;
    bool getCurrentAntiAliasing() const;

protected:
    // 成员变量
    WhiteboardTypes::DrawingType m_toolType;
    bool m_isDrawing;  // 当前是否正在绘制
    QGraphicsScene *m_scene;  // 场景引用

private:
    Q_DISABLE_COPY(BaseTool)
};

#endif // BASETOOL_H
