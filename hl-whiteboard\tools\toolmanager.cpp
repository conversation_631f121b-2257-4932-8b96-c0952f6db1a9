#include "toolmanager.h"
#include "base/basetool.h"
#include "pentool.h"
#include "rectangletool.h"
#include "ellipsetool.h"
#include "linetool.h"
#include <QGraphicsScene>
#include <QDateTime>
#include <QDebug>
#include "../utils/DrawingPerformanceProfiler.h"


ToolManager::ToolManager(QObject *parent)
    : QObject(parent)
    , m_currentTool(WhiteboardTypes::DrawingType::Pen)
    , m_scene(nullptr)
    , m_penColor(Qt::black)
    , m_penWidth(2.0)
    , m_brushColor(Qt::transparent)
    , m_antiAliasing(true)
{
    createTools();
}

ToolManager::~ToolManager()
{
    destroyTools();
}

void ToolManager::createTools()
{
    // 创建所有工具实例
    m_tools[WhiteboardTypes::DrawingType::Pen] = new PenTool(this);
    m_tools[WhiteboardTypes::DrawingType::Rectangle] = new RectangleTool(this);
    m_tools[WhiteboardTypes::DrawingType::Ellipse] = new EllipseTool(this);
    m_tools[WhiteboardTypes::DrawingType::Line] = new LineTool(this);

    // 连接工具信号并设置场景
    for (auto it = m_tools.begin(); it != m_tools.end(); ++it) {
        BaseTool *tool = it.value();
        connect(tool, &BaseTool::itemCreated, this, &ToolManager::onToolItemCreated);
        connect(tool, &BaseTool::itemFinished, this, &ToolManager::onToolItemFinished);

        // 如果场景已经设置，则为工具设置场景
        if (m_scene) {
            tool->setScene(m_scene);
        }
    }
}

void ToolManager::destroyTools()
{
    // 手动删除所有工具实例
    qDeleteAll(m_tools);
    m_tools.clear();
}

BaseTool* ToolManager::createTool(WhiteboardTypes::DrawingType type)
{
    switch (type) {
    case WhiteboardTypes::DrawingType::Pen:
        return new PenTool(this);
    case WhiteboardTypes::DrawingType::Rectangle:
        return new RectangleTool(this);
    case WhiteboardTypes::DrawingType::Ellipse:
        return new EllipseTool(this);
    case WhiteboardTypes::DrawingType::Line:
        return new LineTool(this);
    default:
        qWarning() << "Unsupported tool type:" << static_cast<int>(type);
        return nullptr;
    }
}

void ToolManager::setCurrentTool(WhiteboardTypes::DrawingType tool)
{
    if (m_currentTool != tool) {
        // 结束当前工具的所有操作
        BaseTool *currentTool = getCurrentToolInstance();
        if (currentTool) {
            currentTool->finishCurrentOperation();
        }



        m_currentTool = tool;

        emit toolChanged(tool);
    }
}

WhiteboardTypes::DrawingType ToolManager::currentTool() const
{
    return m_currentTool;
}

BaseTool* ToolManager::getCurrentToolInstance() const
{
    return getToolInstance(m_currentTool);
}

BaseTool* ToolManager::getToolInstance(WhiteboardTypes::DrawingType tool) const
{
    auto it = m_tools.find(tool);
    return (it != m_tools.end()) ? it.value() : nullptr;
}

void ToolManager::setScene(QGraphicsScene *scene)
{
    if (m_scene != scene) {
        m_scene = scene;

        // 更新所有工具的场景引用
        for (auto it = m_tools.begin(); it != m_tools.end(); ++it) {
            BaseTool *tool = it.value();
            if (tool) {
                tool->setScene(scene);
            }
        }
    }
}

QGraphicsScene* ToolManager::scene() const
{
    return m_scene;
}

void ToolManager::setPenColor(const QColor &color)
{
    if (m_penColor != color) {
        m_penColor = color;
    }
}

QColor ToolManager::penColor() const
{
    return m_penColor;
}

void ToolManager::setPenWidth(qreal width)
{
    if (qAbs(m_penWidth - width) > 0.001) {
        m_penWidth = width;
    }
}

qreal ToolManager::penWidth() const
{
    return m_penWidth;
}

void ToolManager::setBrushColor(const QColor &color)
{
    if (m_brushColor != color) {
        m_brushColor = color;
    }
}

QColor ToolManager::brushColor() const
{
    return m_brushColor;
}

void ToolManager::setAntiAliasing(bool enabled)
{
    if (m_antiAliasing != enabled) {
        m_antiAliasing = enabled;
    }
}

bool ToolManager::antiAliasing() const
{
    return m_antiAliasing;
}



// 统一的输入事件处理
void ToolManager::handleInputPress(int inputId, const QPointF &scenePos, bool isTouch)
{
    // 检查多指绘制支持
    if (isTouch && !WhiteboardTypes::supportsMultiTouch(m_currentTool)) {
        return;
    }

    BaseTool *tool = getCurrentToolInstance();
    if (tool) {
        // 统一调用输入接口
        tool->onInputPress(inputId, scenePos);
    }
}

void ToolManager::handleInputMove(int inputId, const QPointF &scenePos, bool isTouch)
{
    DRAWING_TIMER("ToolManager::handleInputMove");

    BaseTool *tool = getCurrentToolInstance();
    if (tool) {
        tool->onInputMove(inputId, scenePos);
    }
}

void ToolManager::handleInputRelease(int inputId, const QPointF &scenePos, bool isTouch)
{

    BaseTool *tool = getCurrentToolInstance();
    if (tool) {
        tool->onInputRelease(inputId, scenePos);


    }
}

// 便捷的鼠标事件接口
void ToolManager::handleMousePress(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        handleInputPress(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
    }
}

void ToolManager::handleMouseMove(QGraphicsSceneMouseEvent *event)
{
    handleInputMove(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
}

void ToolManager::handleMouseRelease(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        handleInputRelease(WhiteboardTypes::MOUSE_INPUT_ID, event->scenePos(), false);
    }
}

bool ToolManager::isToolActive() const
{
    // 检查当前工具是否有活跃操作
    BaseTool *tool = getCurrentToolInstance();
    return tool && tool->isDrawing();
}

void ToolManager::onToolItemCreated(QGraphicsItem *item)
{
    if (item) {
        emit itemCreated(item);
    }
}

void ToolManager::onToolItemFinished(QGraphicsItem *item)
{
    if (item) {
        emit itemFinished(item);
    }
}
