#include "RectItem.h"
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QPainterPath>
#include <QtMath>
#include <QDebug>

RectItem::RectItem(QGraphicsItem *parent)
    : QGraphicsRectItem(parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Rectangle, this)
{
}

RectItem::RectItem(const QRectF &rect, QGraphicsItem *parent)
    : QGraphicsRectItem(rect, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Rectangle, this)
{
}

RectItem::RectItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem *parent)
    : QGraphicsRectItem(x, y, width, height, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Rectangle, this)
{
}

RectItem::~RectItem()
{
}

QJsonObject RectItem::serializeGeometry() const
{
    QJsonObject json;

    // 矩形几何信息
    QRectF currentRect = rect();
    QJsonObject rectObj;
    rectObj["x"] = currentRect.x();
    rectObj["y"] = currentRect.y();
    rectObj["width"] = currentRect.width();
    rectObj["height"] = currentRect.height();
    json["rect"] = rectObj;

    return json;
}

bool RectItem::deserializeGeometry(const QJsonObject &json)
{
    try {
        // 读取矩形几何信息
        QJsonObject rectObj = json["rect"].toObject();
        QRectF newRect(
            rectObj["x"].toDouble(),
            rectObj["y"].toDouble(),
            rectObj["width"].toDouble(),
            rectObj["height"].toDouble()
        );
        setRect(newRect);

        return true;
    } catch (...) {
        qWarning() << "RectItem::deserializeGeometry: Exception occurred";
        return false;
    }
}

void RectItem::setRectangle(const QRectF &rect)
{
    setRect(rect);
}

void RectItem::setRectangle(qreal x, qreal y, qreal width, qreal height)
{
    setRectangle(QRectF(x, y, width, height));
}

QRectF RectItem::rectangle() const
{
    return rect();
}

void RectItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // 设置抗锯齿
    setupAntiAliasing(painter);

    // 使用默认的矩形绘制
    QGraphicsRectItem::paint(painter, option, widget);
}

