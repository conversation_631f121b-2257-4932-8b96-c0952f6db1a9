#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "hl-whiteboard/whiteboard.h"
#include "hl-whiteboard/types/whiteboardtypes.h"

QT_BEGIN_NAMESPACE
class QVBoxLayout;
class QHBoxLayout;
class QPushButton;
class QWidget;
class QSpinBox;
class QLabel;
class QCheckBox;
QT_END_NAMESPACE


QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 工具切换槽
    void onPenToolClicked();
    void onRectangleToolClicked();
    void onEllipseToolClicked();
    void onLineToolClicked();

    // 样式设置槽
    void onColorButtonClicked();
    void onWidthChanged(int width);
    void onAntiAliasingToggled(bool enabled);
    void onMultiTouchToggled(bool enabled);

    // 白板事件槽
    void onToolChanged(WhiteboardTypes::DrawingType tool);
    void onSceneChanged();

private:
    void setupUI();
    void setupToolbar();
    void setupWhiteboard();
    void updateToolButtons();
    void updateColorButton();

    Ui::MainWindow *ui;

    // 白板组件
    Whiteboard *m_whiteboard;

    // UI 组件
    QWidget *centralWidget;
    QVBoxLayout *mainLayout;
    QHBoxLayout *toolbarLayout;

    // 工具按钮
    QPushButton *m_penButton;
    QPushButton *m_rectangleButton;
    QPushButton *m_ellipseButton;
    QPushButton *m_lineButton;

    // 样式控件
    QPushButton *m_colorButton;
    QSpinBox *m_widthSpinBox;
    QLabel *m_widthLabel;

    // 功能开关
    QCheckBox *m_antiAliasingCheckBox;
    QCheckBox *m_multiTouchCheckBox;
};
#endif // MAINWINDOW_H
