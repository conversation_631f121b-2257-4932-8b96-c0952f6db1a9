#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QWidget>
#include <QColorDialog>
#include <QSpinBox>
#include <QLabel>
#include <QCheckBox>


MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_whiteboard(nullptr)
    , centralWidget(nullptr)
    , mainLayout(nullptr)
    , toolbarLayout(nullptr)
    , m_penButton(nullptr)
    , m_rectangleButton(nullptr)
    , m_ellipseButton(nullptr)
    , m_lineButton(nullptr)
    , m_colorButton(nullptr)
    , m_widthSpinBox(nullptr)
    , m_widthLabel(nullptr)
    , m_antiAliasingCheckBox(nullptr)
    , m_multiTouchCheckBox(nullptr)
{
    ui->setupUi(this);
    setupUI();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupUI()
{
    // 设置窗口属性
    setWindowTitle("Qt Graphics View - Whiteboard Demo");
    resize(1200, 800);

    // 创建中央窗口部件
    centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    mainLayout = new QVBoxLayout(centralWidget);

    // 设置工具栏和白板
    setupToolbar();
    setupWhiteboard();
}

void MainWindow::setupToolbar()
{
    // 创建工具栏布局
    toolbarLayout = new QHBoxLayout();

    // 创建工具按钮
    m_penButton = new QPushButton("画笔", this);
    m_rectangleButton = new QPushButton("矩形", this);
    m_ellipseButton = new QPushButton("椭圆", this);
    m_lineButton = new QPushButton("直线", this);

    // 设置按钮为可选中状态
    m_penButton->setCheckable(true);
    m_rectangleButton->setCheckable(true);
    m_ellipseButton->setCheckable(true);
    m_lineButton->setCheckable(true);
    m_penButton->setChecked(true); // 默认选中画笔

    // 添加工具按钮到布局
    toolbarLayout->addWidget(m_penButton);
    toolbarLayout->addWidget(m_rectangleButton);
    toolbarLayout->addWidget(m_ellipseButton);
    toolbarLayout->addWidget(m_lineButton);

    toolbarLayout->addSpacing(20);

    // 创建颜色按钮
    m_colorButton = new QPushButton("颜色", this);
    m_colorButton->setStyleSheet("QPushButton { background-color: black; color: white; }");
    toolbarLayout->addWidget(m_colorButton);

    toolbarLayout->addStretch(); // 添加弹性空间

    // 将工具栏添加到主布局
    mainLayout->addLayout(toolbarLayout);

    connect(m_penButton, &QPushButton::clicked, this, &MainWindow::onPenToolClicked);
    connect(m_rectangleButton, &QPushButton::clicked, this, &MainWindow::onRectangleToolClicked);
    connect(m_ellipseButton, &QPushButton::clicked, this, &MainWindow::onEllipseToolClicked);
    connect(m_lineButton, &QPushButton::clicked, this, &MainWindow::onLineToolClicked);
    connect(m_colorButton, &QPushButton::clicked, this, &MainWindow::onColorButtonClicked);
}

void MainWindow::setupWhiteboard()
{
    // 创建白板组件
    m_whiteboard = new Whiteboard(this);

    // 连接白板信号
    connect(m_whiteboard, &Whiteboard::toolChanged, this, &MainWindow::onToolChanged);
    connect(m_whiteboard, &Whiteboard::sceneChanged, this, &MainWindow::onSceneChanged);

    // 将白板添加到主布局
    mainLayout->addWidget(m_whiteboard, 1); // 设置拉伸因子为1，占据剩余空间

    // 设置初始状态
    m_whiteboard->setCurrentTool(WhiteboardTypes::DrawingType::Pen);
    m_whiteboard->setPenColor(Qt::black);
    m_whiteboard->setPenWidth(2);
    m_whiteboard->setAntiAliasing(true);
}

// 工具切换槽实现
void MainWindow::onPenToolClicked()
{
    m_whiteboard->setCurrentTool(WhiteboardTypes::DrawingType::Pen);
}

void MainWindow::onRectangleToolClicked()
{
    m_whiteboard->setCurrentTool(WhiteboardTypes::DrawingType::Rectangle);
}

void MainWindow::onEllipseToolClicked()
{
    m_whiteboard->setCurrentTool(WhiteboardTypes::DrawingType::Ellipse);
}

void MainWindow::onLineToolClicked()
{
    m_whiteboard->setCurrentTool(WhiteboardTypes::DrawingType::Line);
}

void MainWindow::onColorButtonClicked()
{
    if (!m_whiteboard) return;

    QColor currentColor = m_whiteboard->penColor();
    QColor color = QColorDialog::getColor(currentColor, this, "选择画笔颜色");

    if (color.isValid()) {
        m_whiteboard->setPenColor(color);
        updateColorButton();
    }
}

void MainWindow::onWidthChanged(int width)
{
    if (m_whiteboard) {
        m_whiteboard->setPenWidth(width);
    }
}

void MainWindow::onAntiAliasingToggled(bool enabled)
{
    if (m_whiteboard) {
        m_whiteboard->setAntiAliasing(enabled);
    }
}

void MainWindow::onMultiTouchToggled(bool enabled)
{
    if (m_whiteboard) {
        m_whiteboard->setMultiTouchEnabled(enabled);
    }
}

// 白板事件槽实现
void MainWindow::onToolChanged(WhiteboardTypes::DrawingType tool)
{
    updateToolButtons();
}

void MainWindow::onSceneChanged()
{
    // 场景变化处理
}

// 辅助方法实现
void MainWindow::updateToolButtons()
{
    if (!m_whiteboard) return;

    auto currentTool = m_whiteboard->currentTool();
    m_penButton->setChecked(currentTool == WhiteboardTypes::DrawingType::Pen);
    m_rectangleButton->setChecked(currentTool == WhiteboardTypes::DrawingType::Rectangle);
    m_ellipseButton->setChecked(currentTool == WhiteboardTypes::DrawingType::Ellipse);
    m_lineButton->setChecked(currentTool == WhiteboardTypes::DrawingType::Line);
}

void MainWindow::updateColorButton()
{
    if (!m_whiteboard) return;

    QColor color = m_whiteboard->penColor();
    QString style = QString("QPushButton { background-color: %1; color: white; }").arg(color.name());
    m_colorButton->setStyleSheet(style);
}

