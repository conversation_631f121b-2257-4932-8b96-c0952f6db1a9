#ifndef EllipseItem_H
#define EllipseItem_H

#include <QGraphicsEllipseItem>
#include <QJsonObject>
#include <QRectF>
#include <QPointF>
#include "base/drawingitembase.h"

/**
 * @brief EllipseItem - 椭圆绘图项
 *
 * 继承自 QGraphicsEllipseItem 和 DrawingItemBase，实现椭圆绘制功能。
 * 支持抗锯齿绘制和 JSON 序列化。
 */
class EllipseItem : public QGraphicsEllipseItem, public DrawingItemBase
{
public:
    explicit EllipseItem(QGraphicsItem *parent = nullptr);
    explicit EllipseItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    explicit EllipseItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem *parent = nullptr);
    ~EllipseItem();
    
    // 实现 DrawingItemBase 的几何序列化接口
    QJsonObject serializeGeometry() const override;
    bool deserializeGeometry(const QJsonObject &json) override;
    
    // 椭圆特有功能
    void setEllipse(const QRectF &rect);
    void setEllipse(qreal x, qreal y, qreal width, qreal height);
    QRectF ellipse() const;

    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;


private:
};

#endif // EllipseItem_H
