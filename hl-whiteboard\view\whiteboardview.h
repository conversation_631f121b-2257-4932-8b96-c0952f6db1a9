#ifndef WHITEBOARDVIEW_H
#define WHITEBOARDVIEW_H

#include <QGraphicsView>
#include <QWheelEvent>
#include <QMouseEvent>
#include <QGestureEvent>
#include "../types/whiteboardtypes.h"

class QGraphicsScene;
class WhiteboardScene;
class EventCoordinator;

/**
 * @brief WhiteboardView - QGraphicsView 视图层实现
 * 
 * 负责处理用户交互、视图变换、缩放平移等视图层功能
 * 继承自 QGraphicsView，提供白板专用的视图控制
 */
class WhiteboardView : public QGraphicsView
{
    Q_OBJECT

public:
    explicit WhiteboardView(QWidget *parent = nullptr);
    explicit WhiteboardView(QGraphicsScene *scene, QWidget *parent = nullptr);
    ~WhiteboardView();

    // 事件协调器设置
    void setEventCoordinator(EventCoordinator *coordinator);
    EventCoordinator* eventCoordinator() const;

    // 基本视图控制
    void fitToWindow();
    void centerContent();

    // 平移控制
    void panTo(const QPointF &scenePos);
    void panBy(const QPointF &delta);
    void centerOnPoint(const QPointF &scenePos);

    // 视图模式设置
    void setDragMode(QGraphicsView::DragMode mode);
    void setInteractionMode(bool drawingMode);
    bool isDrawingMode() const;

    // 网格和背景
    void setGridVisible(bool visible);
    bool isGridVisible() const;
    void setGridSize(qreal size);
    qreal gridSize() const;

    // 抗锯齿设置
    void setAntiAliasing(bool enabled);
    bool antiAliasing() const;



    // 视图状态查询
    QRectF visibleSceneRect() const;
    QPointF mapToScene(const QPoint &viewPos) const;
    QPoint mapFromScene(const QPointF &scenePos) const;

signals:
    // 视图变换信号
    void viewTransformed();
    
    // 交互模式信号
    void interactionModeChanged(bool drawingMode);
    
    // 用户交互信号
    void sceneClicked(const QPointF &scenePos, Qt::MouseButton button);
    void sceneDragged(const QPointF &scenePos, Qt::MouseButtons buttons);
    void sceneDoubleClicked(const QPointF &scenePos);

protected:
    // 重写事件处理
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    
    // 手势事件处理
    bool event(QEvent *event) override;
    bool gestureEvent(QGestureEvent *event);
    void panTriggered(QPanGesture *gesture);

    // 触摸事件处理
    bool touchEvent(QTouchEvent *event);

    // 绘制网格背景
    void drawBackground(QPainter *painter, const QRectF &rect) override;

private slots:
    void onSceneRectChanged(const QRectF &rect);

private:
    void setupView();
    void emitViewTransformed();

    // 私有成员变量
    bool m_drawingMode;
    bool m_gridVisible;
    qreal m_gridSize;
    bool m_antiAliasing;

    // 交互状态
    bool m_panning;
    QPoint m_lastPanPoint;

    // 事件协调器
    EventCoordinator *m_eventCoordinator;
    
    Q_DISABLE_COPY(WhiteboardView)
};

#endif // WHITEBOARDVIEW_H