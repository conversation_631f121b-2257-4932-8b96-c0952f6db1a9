#include "scenemanager.h"
#include "whiteboardscene.h"
#include "../items/base/drawingitembase.h"
#include <QGraphicsItem>
#include <QGraphicsItemGroup>
#include <QGraphicsSceneMouseEvent>
#include <QJsonDocument>
#include <QDebug>
#include <QApplication>
#include <algorithm>
#include <cstring>

// 静态常量定义
const qreal SceneManager::DEFAULT_EXPAND_MARGIN = 1000.0;
const int SceneManager::DEFAULT_MAX_ITEM_COUNT = 10000;
const int SceneManager::DEFAULT_CLEANUP_INTERVAL = 30000; // 30秒
const size_t SceneManager::MEMORY_WARNING_THRESHOLD = 100 * 1024 * 1024; // 100MB

SceneManager::SceneManager(QObject *parent)
    : QObject(parent)
    , m_scene(nullptr)
    , m_infiniteSceneEnabled(true)
    , m_sceneExpandMargin(DEFAULT_EXPAND_MARGIN)
    , m_lastSceneBounds()
    , m_lastContentBounds()
    , m_hasUnsavedChanges(false)
    , m_maxItemCount(DEFAULT_MAX_ITEM_COUNT)
    , m_autoCleanupEnabled(true)
    , m_cleanupTimer(nullptr)
    , m_lastMemoryUsage(0)
    , m_statisticsValid(false)
{
    setupManager();
}

SceneManager::~SceneManager()
{
    disconnectSignals();
    clearAllItems();
}

void SceneManager::setupManager()
{
    // 创建清理定时器
    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(DEFAULT_CLEANUP_INTERVAL);
    m_cleanupTimer->setSingleShot(false);
    
    connect(m_cleanupTimer, &QTimer::timeout,
            this, &SceneManager::onCleanupTimer);
    
    if (m_autoCleanupEnabled) {
        m_cleanupTimer->start();
    }
    
    // 初始化统计信息
    memset(&m_cachedStatistics, 0, sizeof(SceneStatistics));
    
    qDebug() << "SceneManager initialized with expand margin:" << m_sceneExpandMargin;
}

void SceneManager::connectSignals()
{
    if (!m_scene) return;
    
    // 连接场景鼠标事件信号（用于无限扩展场景支持）
    connect(m_scene, &WhiteboardScene::sceneMousePress,
            this, &SceneManager::onSceneMousePress);
    connect(m_scene, &WhiteboardScene::sceneMouseMove,
            this, &SceneManager::onSceneMouseMove);
    connect(m_scene, &WhiteboardScene::sceneMouseRelease,
            this, &SceneManager::onSceneMouseRelease);
    
    qDebug() << "SceneManager signals connected to scene";
}

void SceneManager::disconnectSignals()
{
    if (!m_scene) return;
    
    // 断开场景信号
    disconnect(m_scene, &WhiteboardScene::sceneMousePress,
               this, &SceneManager::onSceneMousePress);
    disconnect(m_scene, &WhiteboardScene::sceneMouseMove,
               this, &SceneManager::onSceneMouseMove);
    disconnect(m_scene, &WhiteboardScene::sceneMouseRelease,
               this, &SceneManager::onSceneMouseRelease);
    
    qDebug() << "SceneManager signals disconnected from scene";
}

// 场景关联实现
void SceneManager::setScene(WhiteboardScene *scene)
{
    if (m_scene == scene) return;
    
    // 断开旧场景的连接
    if (m_scene) {
        disconnectSignals();
    }
    
    m_scene = scene;
    
    // 连接新场景的信号
    if (m_scene) {
        connectSignals();
        
        // 设置场景管理器到场景中
        m_scene->setSceneManager(this);
        
        // 初始化场景边界
        updateSceneBounds();
    }
    
    emit sceneStateChanged();
    qDebug() << "SceneManager associated with scene:" << scene;
}

WhiteboardScene* SceneManager::scene() const
{
    return m_scene;
}

// 图形项生命周期管理实现
void SceneManager::registerItem(DrawingItemBase *item)
{
    if (!item || m_managedItems.contains(item)) {
        return;
    }
    
    // 检查是否达到最大项数限制
    if (m_managedItems.count() >= m_maxItemCount) {
        qWarning() << "Maximum item count reached:" << m_maxItemCount;
        emit maxItemCountReached();
        return;
    }
    
    addItemToRegistry(item);
    
    // 更新场景边界以包含新项
    if (QGraphicsItem *graphicsItem = dynamic_cast<QGraphicsItem*>(item)) {
        QRectF itemBounds = graphicsItem->sceneBoundingRect();
        if (itemBounds.isValid()) {
            expandSceneToInclude(itemBounds);
        }
    }
    
    markAsDirty();
    m_statisticsValid = false;
    
    emit itemRegistered(item);
    qDebug() << "Item registered, total count:" << m_managedItems.count();
}

void SceneManager::unregisterItem(DrawingItemBase *item)
{
    if (!item || !m_managedItems.contains(item)) {
        return;
    }
    
    removeItemFromRegistry(item);
    
    // 添加到待清理列表
    if (!m_pendingCleanup.contains(item)) {
        m_pendingCleanup.append(item);
    }
    
    markAsDirty();
    m_statisticsValid = false;
    
    emit itemUnregistered(item);
    qDebug() << "Item unregistered, total count:" << m_managedItems.count();
}

void SceneManager::cleanupDestroyedItems()
{
    int cleanedCount = 0;
    
    // 清理已标记为待清理的项
    for (auto it = m_pendingCleanup.begin(); it != m_pendingCleanup.end();) {
        DrawingItemBase *item = *it;
        if (!isItemValid(item)) {
            it = m_pendingCleanup.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
    
    // 验证管理列表中的项
    for (auto it = m_managedItems.begin(); it != m_managedItems.end();) {
        DrawingItemBase *item = *it;
        if (!isItemValid(item)) {
            it = m_managedItems.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
    
    if (cleanedCount > 0) {
        m_statisticsValid = false;
        emit itemsCleanedUp(cleanedCount);
        qDebug() << "Cleaned up" << cleanedCount << "destroyed items";
    }
}

QList<DrawingItemBase*> SceneManager::managedItems() const
{
    return m_managedItems;
}

int SceneManager::itemCount() const
{
    return m_managedItems.count();
}

int SceneManager::itemCount(WhiteboardTypes::DrawingType type) const
{
    int count = 0;
    for (DrawingItemBase *item : m_managedItems) {
        if (item && item->drawingType() == type) {
            count++;
        }
    }
    return count;
}

// 无限扩展场景支持实现
void SceneManager::enableInfiniteScene(bool enable)
{
    if (m_infiniteSceneEnabled == enable) return;
    
    m_infiniteSceneEnabled = enable;
    
    if (enable) {
        // 启用无限场景时，确保当前内容可见
        optimizeSceneBounds();
    }
    
    emit sceneStateChanged();
    qDebug() << "Infinite scene" << (enable ? "enabled" : "disabled");
}

bool SceneManager::isInfiniteSceneEnabled() const
{
    return m_infiniteSceneEnabled;
}

void SceneManager::setSceneExpandMargin(qreal margin)
{
    if (qFuzzyCompare(m_sceneExpandMargin, margin)) return;
    
    m_sceneExpandMargin = qMax(0.0, margin);
    emit sceneStateChanged();
    qDebug() << "Scene expand margin set to:" << m_sceneExpandMargin;
}

qreal SceneManager::sceneExpandMargin() const
{
    return m_sceneExpandMargin;
}

void SceneManager::expandSceneToInclude(const QRectF &rect)
{
    if (!m_scene || !m_infiniteSceneEnabled || !rect.isValid()) {
        return;
    }
    
    QRectF currentRect = m_scene->sceneRect();
    QRectF newRect = currentRect.united(rect);
    
    // 添加边距以支持进一步扩展
    newRect = newRect.adjusted(-m_sceneExpandMargin, -m_sceneExpandMargin,
                              m_sceneExpandMargin, m_sceneExpandMargin);
    
    if (newRect != currentRect) {
        m_scene->setSceneRect(newRect);
        m_lastSceneBounds = newRect;
        emit sceneBoundsChanged(newRect);
        qDebug() << "Scene expanded to include rect:" << rect << "-> new bounds:" << newRect;
    }
}

void SceneManager::expandSceneToInclude(const QPointF &point)
{
    if (!point.isNull()) {
        QRectF pointRect(point.x() - 1, point.y() - 1, 2, 2);
        expandSceneToInclude(pointRect);
    }
}

void SceneManager::ensurePointVisible(const QPointF &point)
{
    if (!m_scene || point.isNull()) return;
    
    QRectF currentRect = m_scene->sceneRect();
    
    // 检查点是否在当前场景矩形内
    if (currentRect.contains(point)) {
        return;  // 点已经可见，无需扩展
    }
    
    // 创建包含该点的矩形
    QRectF pointRect(point.x() - m_sceneExpandMargin/2, 
                     point.y() - m_sceneExpandMargin/2,
                     m_sceneExpandMargin, 
                     m_sceneExpandMargin);
    
    expandSceneToInclude(pointRect);
}

QRectF SceneManager::currentSceneBounds() const
{
    return m_scene ? m_scene->sceneRect() : QRectF();
}

QRectF SceneManager::contentBounds() const
{
    return calculateContentBounds();
}

void SceneManager::optimizeSceneBounds()
{
    if (!m_scene || !m_infiniteSceneEnabled) return;
    
    QRectF contentRect = calculateContentBounds();
    if (contentRect.isValid()) {
        // 添加适当的边距
        contentRect = contentRect.adjusted(-m_sceneExpandMargin, -m_sceneExpandMargin,
                                          m_sceneExpandMargin, m_sceneExpandMargin);
        
        m_scene->setSceneRect(contentRect);
        m_lastSceneBounds = contentRect;
        emit sceneBoundsChanged(contentRect);
        qDebug() << "Scene bounds optimized to:" << contentRect;
    }
}

// 场景状态管理实现
void SceneManager::saveSceneState()
{
    if (!m_scene) return;
    
    m_savedSceneState = exportSceneData();
    markAsClean();
    qDebug() << "Scene state saved";
}

void SceneManager::restoreSceneState()
{
    if (!m_scene || m_savedSceneState.isEmpty()) return;
    
    importSceneData(m_savedSceneState);
    markAsClean();
    qDebug() << "Scene state restored";
}

void SceneManager::clearSceneState()
{
    m_savedSceneState = QJsonObject();
    markAsClean();
    qDebug() << "Scene state cleared";
}

bool SceneManager::hasUnsavedChanges() const
{
    return m_hasUnsavedChanges;
}

void SceneManager::markAsClean()
{
    if (m_hasUnsavedChanges) {
        m_hasUnsavedChanges = false;
        emit sceneStateChanged();
    }
}

void SceneManager::markAsDirty()
{
    if (!m_hasUnsavedChanges) {
        m_hasUnsavedChanges = true;
        emit sceneStateChanged();
    }
}

// 内存和性能管理实现
void SceneManager::setMaxItemCount(int maxCount)
{
    m_maxItemCount = qMax(1, maxCount);
    qDebug() << "Max item count set to:" << m_maxItemCount;
}

int SceneManager::maxItemCount() const
{
    return m_maxItemCount;
}

void SceneManager::enableAutoCleanup(bool enable)
{
    if (m_autoCleanupEnabled == enable) return;
    
    m_autoCleanupEnabled = enable;
    
    if (enable && m_cleanupTimer) {
        m_cleanupTimer->start();
    } else if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
    
    qDebug() << "Auto cleanup" << (enable ? "enabled" : "disabled");
}

bool SceneManager::isAutoCleanupEnabled() const
{
    return m_autoCleanupEnabled;
}

void SceneManager::setCleanupInterval(int intervalMs)
{
    int interval = qMax(1000, intervalMs); // 最小1秒
    
    if (m_cleanupTimer) {
        m_cleanupTimer->setInterval(interval);
    }
    
    qDebug() << "Cleanup interval set to:" << interval << "ms";
}

int SceneManager::cleanupInterval() const
{
    return m_cleanupTimer ? m_cleanupTimer->interval() : 0;
}

void SceneManager::performCleanup()
{
    cleanupDestroyedItems();
    performMemoryOptimization();
    
    size_t currentUsage = estimateMemoryUsage();
    if (currentUsage != m_lastMemoryUsage) {
        m_lastMemoryUsage = currentUsage;
        emit memoryUsageChanged(currentUsage);
    }
    
    emit cleanupPerformed();
    qDebug() << "Cleanup performed, memory usage:" << currentUsage << "bytes";
}

size_t SceneManager::estimateMemoryUsage() const
{
    size_t totalUsage = 0;
    
    // 估算每个图形项的内存使用
    for (DrawingItemBase *item : m_managedItems) {
        if (item) {
            totalUsage += calculateItemMemoryUsage(item);
        }
    }
    
    // 添加管理器自身的内存使用
    totalUsage += sizeof(SceneManager);
    totalUsage += m_managedItems.capacity() * sizeof(DrawingItemBase*);
    totalUsage += m_pendingCleanup.capacity() * sizeof(DrawingItemBase*);
    
    return totalUsage;
}

// 与其他管理器的协调机制实现
void SceneManager::notifyToolManagerChange()
{
    emit coordinationRequested("ToolManager");
}



void SceneManager::handleItemAdded(DrawingItemBase *item)
{
    if (item) {
        registerItem(item);
        updateSceneBounds();
    }
}

void SceneManager::handleItemRemoved(DrawingItemBase *item)
{
    if (item) {
        unregisterItem(item);
    }
}

void SceneManager::handleItemModified(DrawingItemBase *item)
{
    if (item && m_managedItems.contains(item)) {
        // 更新场景边界以包含修改后的项
        if (QGraphicsItem *graphicsItem = dynamic_cast<QGraphicsItem*>(item)) {
            QRectF itemBounds = graphicsItem->sceneBoundingRect();
            if (itemBounds.isValid()) {
                expandSceneToInclude(itemBounds);
            }
        }
        
        markAsDirty();
        m_statisticsValid = false;
    }
}

// 场景数据管理实现
QJsonObject SceneManager::exportSceneData() const
{
    QJsonObject data;
    
    // 导出基本信息
    data["version"] = "1.0";
    data["itemCount"] = m_managedItems.count();
    data["infiniteSceneEnabled"] = m_infiniteSceneEnabled;
    data["sceneExpandMargin"] = m_sceneExpandMargin;
    
    // 导出场景边界
    QRectF bounds = currentSceneBounds();
    data["sceneBounds"] = QJsonObject{
        {"x", bounds.x()},
        {"y", bounds.y()},
        {"width", bounds.width()},
        {"height", bounds.height()}
    };
    
    // 导出内容边界
    QRectF contentBounds = calculateContentBounds();
    data["contentBounds"] = QJsonObject{
        {"x", contentBounds.x()},
        {"y", contentBounds.y()},
        {"width", contentBounds.width()},
        {"height", contentBounds.height()}
    };
    
    // 导出图形项数据
    QJsonArray itemsArray;
    for (DrawingItemBase *item : m_managedItems) {
        if (item) {
            itemsArray.append(item->toJson());
        }
    }
    data["items"] = itemsArray;
    
    // 导出统计信息
    SceneStatistics stats = getStatistics();
    QJsonObject statsObj;
    statsObj["totalItems"] = stats.totalItems;
    statsObj["sceneBoundsArea"] = stats.sceneBoundsArea;
    statsObj["contentBoundsArea"] = stats.contentBoundsArea;
    statsObj["estimatedMemoryUsage"] = static_cast<qint64>(stats.estimatedMemoryUsage);
    data["statistics"] = statsObj;
    
    return data;
}

bool SceneManager::importSceneData(const QJsonObject &data)
{
    if (data.isEmpty()) return false;
    
    // 清除现有数据
    clearAllItems();
    
    // 导入基本设置
    if (data.contains("infiniteSceneEnabled")) {
        enableInfiniteScene(data["infiniteSceneEnabled"].toBool());
    }
    
    if (data.contains("sceneExpandMargin")) {
        setSceneExpandMargin(data["sceneExpandMargin"].toDouble());
    }
    
    // 导入场景边界
    if (data.contains("sceneBounds") && m_scene) {
        QJsonObject boundsObj = data["sceneBounds"].toObject();
        QRectF bounds(boundsObj["x"].toDouble(),
                     boundsObj["y"].toDouble(),
                     boundsObj["width"].toDouble(),
                     boundsObj["height"].toDouble());
        m_scene->setSceneRect(bounds);
        m_lastSceneBounds = bounds;
    }
    
    // 导入图形项数据
    if (data.contains("items")) {
        QJsonArray itemsArray = data["items"].toArray();
        // 注意：这里需要根据具体的图形项类型创建实例
        // 当前只是占位实现，具体实现需要在图形项类完成后补充
        qDebug() << "Scene data contains" << itemsArray.size() << "items (import pending)";
    }
    
    markAsClean();
    m_statisticsValid = false;
    emit sceneStateChanged();
    
    qDebug() << "Scene data imported successfully";
    return true;
}

void SceneManager::clearAllItems()
{
    // 清理所有管理的项
    for (DrawingItemBase *item : m_managedItems) {
        if (item) {
            // 注意：这里不直接删除item，因为它们可能被其他地方管理
            // 只是从管理列表中移除
        }
    }
    
    m_managedItems.clear();
    m_pendingCleanup.clear();
    
    markAsDirty();
    m_statisticsValid = false;
    
    qDebug() << "All items cleared from SceneManager";
}

void SceneManager::resetScene()
{
    clearAllItems();
    
    if (m_scene) {
        // 重置场景到默认状态
        m_scene->setSceneRect(-m_sceneExpandMargin, -m_sceneExpandMargin,
                             2 * m_sceneExpandMargin, 2 * m_sceneExpandMargin);
    }
    
    m_lastSceneBounds = QRectF();
    m_lastContentBounds = QRectF();
    
    clearSceneState();
    emit sceneStateChanged();
    
    qDebug() << "Scene reset to default state";
}

// 统计和调试信息实现
SceneManager::SceneStatistics SceneManager::getStatistics() const
{
    if (m_statisticsValid) {
        return m_cachedStatistics;
    }
    
    SceneStatistics stats;
    memset(&stats, 0, sizeof(SceneStatistics));
    
    // 统计图形项
    stats.totalItems = m_managedItems.count();
    
    for (DrawingItemBase *item : m_managedItems) {
        if (item) {
            int typeIndex = static_cast<int>(item->drawingType());
            if (typeIndex >= 0 && typeIndex <= static_cast<int>(WhiteboardTypes::DrawingType::Group)) {
                stats.itemsByType[typeIndex]++;
            }
        }
    }
    
    // 计算边界面积
    QRectF sceneBounds = currentSceneBounds();
    stats.sceneBoundsArea = sceneBounds.width() * sceneBounds.height();
    
    QRectF contentBounds = calculateContentBounds();
    stats.contentBoundsArea = contentBounds.width() * contentBounds.height();
    
    // 估算内存使用
    stats.estimatedMemoryUsage = estimateMemoryUsage();
    
    // 状态信息
    stats.hasUnsavedChanges = m_hasUnsavedChanges;
    
    // 缓存结果
    m_cachedStatistics = stats;
    m_statisticsValid = true;
    
    return stats;
}

void SceneManager::printDebugInfo() const
{
    SceneStatistics stats = getStatistics();
    
    qDebug() << "=== SceneManager Debug Info ===";
    qDebug() << "Total items:" << stats.totalItems;
    qDebug() << "Items by type:";
    
    const char* typeNames[] = {"Pen", "Eraser", "Rectangle", "Ellipse", "Line", "Lasso", "Group"};
    for (int i = 0; i <= static_cast<int>(WhiteboardTypes::DrawingType::Group); ++i) {
        if (stats.itemsByType[i] > 0) {
            qDebug() << "  " << typeNames[i] << ":" << stats.itemsByType[i];
        }
    }
    
    qDebug() << "Scene bounds area:" << stats.sceneBoundsArea;
    qDebug() << "Content bounds area:" << stats.contentBoundsArea;
    qDebug() << "Estimated memory usage:" << stats.estimatedMemoryUsage << "bytes";
    qDebug() << "Has unsaved changes:" << stats.hasUnsavedChanges;
    qDebug() << "Infinite scene enabled:" << m_infiniteSceneEnabled;
    qDebug() << "Scene expand margin:" << m_sceneExpandMargin;
    qDebug() << "Auto cleanup enabled:" << m_autoCleanupEnabled;
    qDebug() << "Max item count:" << m_maxItemCount;
    qDebug() << "===============================";
}

// 私有槽函数实现
void SceneManager::onCleanupTimer()
{
    if (m_autoCleanupEnabled) {
        performCleanup();
    }
}

void SceneManager::onSceneRectChanged()
{
    updateSceneBounds();
    m_statisticsValid = false;
}

void SceneManager::onItemDestroyed()
{
    // 标记需要清理
    QTimer::singleShot(0, this, &SceneManager::cleanupDestroyedItems);
}

// 场景事件处理槽实现
void SceneManager::onSceneMousePress(QGraphicsSceneMouseEvent *event)
{
    if (!event) return;
    
    // 确保鼠标按下位置可见（无限扩展场景支持）
    ensurePointVisible(event->scenePos());
}

void SceneManager::onSceneMouseMove(QGraphicsSceneMouseEvent *event)
{
    if (!event) return;
    
    // 确保鼠标移动位置可见（无限扩展场景支持）
    ensurePointVisible(event->scenePos());
}

void SceneManager::onSceneMouseRelease(QGraphicsSceneMouseEvent *event)
{
    if (!event) return;
    
    // 鼠标释放后优化场景边界
    QTimer::singleShot(100, this, &SceneManager::optimizeSceneBounds);
}

// 私有辅助方法实现
void SceneManager::updateSceneBounds()
{
    if (!m_scene) return;
    
    QRectF currentBounds = m_scene->sceneRect();
    if (currentBounds != m_lastSceneBounds) {
        m_lastSceneBounds = currentBounds;
        emit sceneBoundsChanged(currentBounds);
    }
    
    QRectF contentBounds = calculateContentBounds();
    if (contentBounds != m_lastContentBounds) {
        m_lastContentBounds = contentBounds;
        emit contentBoundsChanged(contentBounds);
    }
}

void SceneManager::calculateOptimalBounds()
{
    // 计算包含所有内容的最优边界
    QRectF optimalBounds = calculateContentBounds();
    
    if (optimalBounds.isValid()) {
        // 添加边距
        optimalBounds = optimalBounds.adjusted(-m_sceneExpandMargin, -m_sceneExpandMargin,
                                              m_sceneExpandMargin, m_sceneExpandMargin);
        
        if (m_scene && m_infiniteSceneEnabled) {
            m_scene->setSceneRect(optimalBounds);
        }
    }
}

QRectF SceneManager::calculateContentBounds() const
{
    if (!m_scene) return QRectF();
    
    QRectF bounds;
    
    // 计算所有图形项的边界
    for (DrawingItemBase *item : m_managedItems) {
        if (QGraphicsItem *graphicsItem = dynamic_cast<QGraphicsItem*>(item)) {
            QRectF itemBounds = graphicsItem->sceneBoundingRect();
            if (itemBounds.isValid()) {
                if (bounds.isNull()) {
                    bounds = itemBounds;
                } else {
                    bounds = bounds.united(itemBounds);
                }
            }
        }
    }
    
    return bounds;
}

void SceneManager::addItemToRegistry(DrawingItemBase *item)
{
    if (item && !m_managedItems.contains(item)) {
        m_managedItems.append(item);
    }
}

void SceneManager::removeItemFromRegistry(DrawingItemBase *item)
{
    m_managedItems.removeOne(item);
}

bool SceneManager::isItemValid(DrawingItemBase *item) const
{
    // 简单的有效性检查
    // 在实际实现中，可能需要更复杂的验证逻辑
    return item != nullptr;
}

void SceneManager::checkMemoryLimits()
{
    size_t currentUsage = estimateMemoryUsage();
    
    if (currentUsage > MEMORY_WARNING_THRESHOLD) {
        qWarning() << "Memory usage exceeds threshold:" << currentUsage << "bytes";
        
        // 触发内存优化
        performMemoryOptimization();
    }
}

void SceneManager::performMemoryOptimization()
{
    // 清理已销毁的项
    cleanupDestroyedItems();
    
    // 如果仍然超过限制，可以考虑其他优化策略
    // 例如：压缩路径数据、移除不必要的元数据等
}

size_t SceneManager::calculateItemMemoryUsage(DrawingItemBase *item) const
{
    if (!item) return 0;
    
    // 基础内存使用估算
    size_t usage = sizeof(DrawingItemBase);
    
    // 根据图形项类型估算额外内存使用
    // 这里是简化的估算，实际实现可能需要更精确的计算
    switch (item->drawingType()) {
    case WhiteboardTypes::DrawingType::Pen:
        usage += 1000; // 假设路径数据平均1KB
        break;
    case WhiteboardTypes::DrawingType::Rectangle:
    case WhiteboardTypes::DrawingType::Ellipse:
    case WhiteboardTypes::DrawingType::Line:
        usage += 100; // 几何形状数据较小
        break;
    case WhiteboardTypes::DrawingType::Group:
        usage += 500; // 组合项包含子项引用
        break;
    default:
        usage += 200; // 默认估算
        break;
    }
    
    return usage;
}

void SceneManager::updateSceneState()
{
    m_statisticsValid = false;
    emit sceneStateChanged();
}

void SceneManager::validateSceneConsistency()
{
    // 验证场景一致性
    // 检查管理的项是否都在场景中，场景中的项是否都被管理等
    
    if (!m_scene) return;
    
    int inconsistencies = 0;
    
    // 检查管理的项是否在场景中
    for (DrawingItemBase *item : m_managedItems) {
        if (QGraphicsItem *graphicsItem = dynamic_cast<QGraphicsItem*>(item)) {
            if (graphicsItem->scene() != m_scene) {
                qWarning() << "Managed item not in scene";
                inconsistencies++;
            }
        }
    }
    
    if (inconsistencies > 0) {
        qWarning() << "Scene consistency check found" << inconsistencies << "issues";
    }
}
