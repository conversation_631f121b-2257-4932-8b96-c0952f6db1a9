#include "linetool.h"
#include "../items/lineitem.h"
#include <QDebug>

LineTool::LineTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Line, parent)
    , m_currentLine(nullptr)
{
}

LineTool::~LineTool()
{
    finishCurrentOperation();
}

void LineTool::onInputPress(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    m_isDrawing = true;
    m_startPoint = scenePos;

    // 创建新的直线项
    m_currentLine = createNewLine(m_startPoint);
}

void LineTool::onInputMove(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    if (!m_isDrawing || !m_currentLine) {
        return;
    }

    // 直接更新直线
    QLineF line(m_startPoint, scenePos);
    m_currentLine->setLine(line);
}

void LineTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)
    Q_UNUSED(scenePos)

    if (m_isDrawing && m_currentLine) {
        finishLine();
    }

    m_isDrawing = false;
    m_currentLine = nullptr;
}

void LineTool::finishCurrentOperation()
{
    if (m_currentLine) {
        finishLine();
        m_currentLine = nullptr;
    }

    m_isDrawing = false;
    BaseTool::finishCurrentOperation();
}

void LineTool::cancelCurrentOperation()
{
    if (m_currentLine) {
        removeItemFromScene(m_currentLine);
        m_currentLine = nullptr;
    }

    m_isDrawing = false;
    BaseTool::cancelCurrentOperation();
}

LineItem* LineTool::createNewLine(const QPointF &startPoint)
{
    LineItem *lineItem = new LineItem();

    QLineF initialLine(startPoint, startPoint);
    lineItem->setLine(initialLine);

    addItemToScene(lineItem);

    return lineItem;
}

void LineTool::finishLine()
{
    if (m_currentLine) {
        emit itemFinished(m_currentLine);
    }
}


