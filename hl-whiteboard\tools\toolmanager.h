#ifndef TOOLMANAGER_H
#define TOOLMANAGER_H

#include <QObject>
#include <QGraphicsSceneMouseEvent>
#include <QMap>
#include <QColor>
#include <memory>
#include "../types/whiteboardtypes.h"

// 前向声明
class BaseTool;
class QGraphicsScene;
class QGraphicsItem;

/**
 * @brief 工具管理器类
 * 
 * 负责管理所有绘图工具，处理工具切换，以及将事件分发给当前活动的工具。
 * 支持多指绘制功能，为每个触摸点维护独立的绘图状态。
 */
class ToolManager : public QObject
{
    Q_OBJECT

public:
    explicit ToolManager(QObject *parent = nullptr);
    ~ToolManager();

    // 工具管理
    void setCurrentTool(WhiteboardTypes::DrawingType tool);
    WhiteboardTypes::DrawingType currentTool() const;
    
    BaseTool* getCurrentToolInstance() const;
    BaseTool* getToolInstance(WhiteboardTypes::DrawingType tool) const;

    // 场景设置（用于工具创建图形项）
    void setScene(QGraphicsScene *scene);
    QGraphicsScene* scene() const;

    // 样式设置
    void setPenColor(const QColor &color);
    QColor penColor() const;
    
    void setPenWidth(qreal width);
    qreal penWidth() const;
    
    void setBrushColor(const QColor &color);
    QColor brushColor() const;
    
    void setAntiAliasing(bool enabled);
    bool antiAliasing() const;

    // 统一的输入事件处理（支持鼠标和触屏）
    void handleInputPress(int inputId, const QPointF &scenePos, bool isTouch = false);
    void handleInputMove(int inputId, const QPointF &scenePos, bool isTouch = false);
    void handleInputRelease(int inputId, const QPointF &scenePos, bool isTouch = false);

    // 便捷的鼠标事件接口（内部转换为统一输入事件）
    void handleMousePress(QGraphicsSceneMouseEvent *event);
    void handleMouseMove(QGraphicsSceneMouseEvent *event);
    void handleMouseRelease(QGraphicsSceneMouseEvent *event);
    


    // 工具状态查询
    bool isToolActive() const;

signals:
    void toolChanged(WhiteboardTypes::DrawingType tool);
    void itemCreated(QGraphicsItem *item);
    void itemFinished(QGraphicsItem *item);

private slots:
    void onToolItemCreated(QGraphicsItem *item);
    void onToolItemFinished(QGraphicsItem *item);

private:
    // 工具创建和管理
    void createTools();
    void destroyTools();
    BaseTool* createTool(WhiteboardTypes::DrawingType type);
    


    // 成员变量
    WhiteboardTypes::DrawingType m_currentTool;
    QMap<WhiteboardTypes::DrawingType, BaseTool*> m_tools;
    QGraphicsScene *m_scene;
    
    // 样式设置
    QColor m_penColor;
    qreal m_penWidth;
    QColor m_brushColor;
    bool m_antiAliasing;

    // 禁用拷贝构造和赋值
    Q_DISABLE_COPY(ToolManager)
};

#endif // TOOLMANAGER_H
