#include "whiteboard.h"
#include "scene/scenemanager.h"
#include "scene/whiteboardscene.h"
#include "view/whiteboardview.h"
#include "tools/toolmanager.h"
#include "coordination/eventcoordinator.h"
#include "items/base/drawingitembase.h"
#include <QVBoxLayout>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <memory>

// Pimpl 模式的私有实现类
class WhiteboardPrivate
{
public:
    WhiteboardPrivate(Whiteboard *q);
    ~WhiteboardPrivate();

    // 初始化组件
    void initializeComponents();
    void setupLayout();
    void connectSignals();

    // 工具管理
    WhiteboardTypes::DrawingType currentTool;
    
    // 样式设置
    QColor penColor;
    qreal penWidth;
    QColor brushColor;
    
    // 功能开关
    bool antiAliasing;
    bool multiTouchEnabled;
    
    // 撤销重做状态
    bool canUndoFlag;
    bool canRedoFlag;

    // 交互模式
    Whiteboard::InteractionMode interactionMode;
    
    // 核心组件
    WhiteboardView *view;
    WhiteboardScene *scene;
    SceneManager *sceneManager;
    ToolManager *toolManager;
    EventCoordinator *eventCoordinator;

private:
    Whiteboard *q_ptr;
    Q_DECLARE_PUBLIC(Whiteboard)
};

WhiteboardPrivate::WhiteboardPrivate(Whiteboard *q)
    : q_ptr(q)
    , currentTool(WhiteboardTypes::DrawingType::Pen)
    , penColor(Qt::black)
    , penWidth(2.0)
    , brushColor(Qt::transparent)
    , antiAliasing(true)
    , multiTouchEnabled(false)
    , canUndoFlag(false)
    , canRedoFlag(false)
    , interactionMode(Whiteboard::InteractionMode::Drawing)
    , view(nullptr)
    , scene(nullptr)
    , sceneManager(nullptr)
    , toolManager(nullptr)
    , eventCoordinator(nullptr)
{
}

WhiteboardPrivate::~WhiteboardPrivate()
{
    // 组件会通过Qt的父子关系自动清理
}

void WhiteboardPrivate::initializeComponents()
{
    Q_Q(Whiteboard);

    // 创建场景
    scene = new WhiteboardScene(q);

    // 创建场景管理器
    sceneManager = new SceneManager(q);
    sceneManager->setScene(scene);

    // 创建工具管理器
    toolManager = new ToolManager(q);
    toolManager->setScene(scene);

    // 创建事件协调器
    eventCoordinator = new EventCoordinator(q);
    eventCoordinator->setToolManager(toolManager);
    eventCoordinator->setSceneManager(sceneManager);

    // 创建视图
    view = new WhiteboardView(q);
    view->setScene(scene);
    view->setEventCoordinator(eventCoordinator);

    // 设置组件间的关联
    scene->setSceneManager(sceneManager);
    scene->setEventCoordinator(eventCoordinator);

    // 启用无限扩展场景
    sceneManager->enableInfiniteScene(true);
    sceneManager->setSceneExpandMargin(1000.0);
}

void WhiteboardPrivate::setupLayout()
{
    Q_Q(Whiteboard);
    
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(q);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);
    
    // 添加 WhiteboardView
    if (view) {
        mainLayout->addWidget(view);
    }
}

void WhiteboardPrivate::connectSignals()
{
    Q_Q(Whiteboard);

    // 连接工具管理器信号
    if (toolManager) {
        QObject::connect(toolManager, &ToolManager::toolChanged,
                        q, &Whiteboard::toolChanged);
        // 注意：ToolManager 发出的是 QGraphicsItem* 信号，但 SceneManager 期望 DrawingItemBase*
        // 这里暂时注释掉，需要重新设计信号连接机制
        // QObject::connect(toolManager, &ToolManager::itemCreated,
        //                 sceneManager, &SceneManager::handleItemAdded);
        // QObject::connect(toolManager, &ToolManager::itemFinished,
        //                 sceneManager, &SceneManager::handleItemAdded);
    }

    // 连接事件协调器信号
    if (eventCoordinator) {
        QObject::connect(eventCoordinator, &EventCoordinator::eventModeChanged,
                        [q](EventCoordinator::EventMode mode) {
                            qDebug() << "Event mode changed to:" << static_cast<int>(mode);
                        });
    }

    // 连接视图信号
    if (view) {
        QObject::connect(view, &WhiteboardView::viewTransformed,
                        [q]() {
                            qDebug() << "View transformed";
                        });
        QObject::connect(view, &WhiteboardView::interactionModeChanged,
                        [q](bool drawingMode) {
                            // 通过 Whiteboard 统一管理模式切换
                            auto mode = drawingMode ? Whiteboard::InteractionMode::Drawing
                                                   : Whiteboard::InteractionMode::Selection;
                            q->setInteractionMode(mode);
                        });
    }

    // 连接场景管理器信号
    if (sceneManager) {
        QObject::connect(sceneManager, &SceneManager::sceneStateChanged,
                        q, &Whiteboard::sceneChanged);
    }
}

// Whiteboard 类的实现
Whiteboard::Whiteboard(QWidget *parent)
    : QWidget(parent)
    , d_ptr(new WhiteboardPrivate(this))
{
    Q_D(Whiteboard);
    d->initializeComponents();
    d->setupLayout();
    d->connectSignals();
}

Whiteboard::~Whiteboard()
{
    delete d_ptr;
}

// 工具管理接口实现
void Whiteboard::setCurrentTool(WhiteboardTypes::DrawingType tool)
{
    Q_D(Whiteboard);
    if (d->toolManager) {
        d->toolManager->setCurrentTool(tool);
        d->currentTool = tool;

        // 根据工具类型自动切换交互模式
        if (tool == WhiteboardTypes::DrawingType::Lasso) {
            setInteractionMode(InteractionMode::Selection);
        } else {
            setInteractionMode(InteractionMode::Drawing);
        }

        // toolChanged 信号会通过 ToolManager 的信号连接自动发出
    }
}

WhiteboardTypes::DrawingType Whiteboard::currentTool() const
{
    Q_D(const Whiteboard);
    return d->toolManager ? d->toolManager->currentTool() : d->currentTool;
}

// 样式设置接口实现
void Whiteboard::setPenColor(const QColor &color)
{
    Q_D(Whiteboard);
    if (d->toolManager) {
        d->toolManager->setPenColor(color);
        d->penColor = color;
    }
}

QColor Whiteboard::penColor() const
{
    Q_D(const Whiteboard);
    return d->toolManager ? d->toolManager->penColor() : d->penColor;
}

void Whiteboard::setPenWidth(qreal width)
{
    Q_D(Whiteboard);
    if (d->toolManager) {
        d->toolManager->setPenWidth(width);
        d->penWidth = width;
    }
}

qreal Whiteboard::penWidth() const
{
    Q_D(const Whiteboard);
    return d->toolManager ? d->toolManager->penWidth() : d->penWidth;
}

void Whiteboard::setBrushColor(const QColor &color)
{
    Q_D(Whiteboard);
    if (d->toolManager) {
        d->toolManager->setBrushColor(color);
        d->brushColor = color;
    }
}

QColor Whiteboard::brushColor() const
{
    Q_D(const Whiteboard);
    return d->toolManager ? d->toolManager->brushColor() : d->brushColor;
}

// 撤销重做接口实现
void Whiteboard::undo()
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现 CommandManager 的撤销功能
    // 目前只更新状态
    if (d->canUndoFlag) {
        // 执行撤销操作
        d->canUndoFlag = false; // 临时设置，实际逻辑将在 CommandManager 中实现
        emit canUndoChanged(d->canUndoFlag);
    }
}

void Whiteboard::redo()
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现 CommandManager 的重做功能
    // 目前只更新状态
    if (d->canRedoFlag) {
        // 执行重做操作
        d->canRedoFlag = false; // 临时设置，实际逻辑将在 CommandManager 中实现
        emit canRedoChanged(d->canRedoFlag);
    }
}

bool Whiteboard::canUndo() const
{
    Q_D(const Whiteboard);
    return d->canUndoFlag;
}

bool Whiteboard::canRedo() const
{
    Q_D(const Whiteboard);
    return d->canRedoFlag;
}



// 场景管理接口实现
void Whiteboard::clearScene()
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现场景清空功能
    // 目前只发送信号
    emit sceneChanged();
}

void Whiteboard::saveScene(const QString &filename)
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现场景文件保存功能
    // 目前只是占位实现
    Q_UNUSED(filename)
    // 将通过 exportToJson() 获取数据并保存到文件
}

void Whiteboard::loadScene(const QString &filename)
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现场景文件加载功能
    // 目前只是占位实现
    Q_UNUSED(filename)
    // 将从文件读取数据并通过 importFromJson() 加载
    emit sceneChanged();
}

// JSON 导入导出接口实现
QJsonObject Whiteboard::exportToJson() const
{
    Q_D(const Whiteboard);
    // 这里将在后续任务中实现完整的场景序列化功能
    // 目前返回基本的场景信息
    QJsonObject sceneData;
    sceneData["version"] = "1.0";
    sceneData["tool"] = WhiteboardTypes::drawingTypeToString(d->currentTool);
    sceneData["penColor"] = d->penColor.name();
    sceneData["penWidth"] = d->penWidth;
    sceneData["brushColor"] = d->brushColor.name();
    sceneData["antiAliasing"] = d->antiAliasing;
    sceneData["multiTouchEnabled"] = d->multiTouchEnabled;
    sceneData["items"] = QJsonArray(); // 将在后续任务中填充实际图形项数据
    
    return sceneData;
}

bool Whiteboard::importFromJson(const QJsonObject &json)
{
    Q_D(Whiteboard);
    // 这里将在后续任务中实现完整的场景反序列化功能
    // 目前只加载基本设置
    
    if (!json.contains("version")) {
        return false;
    }
    
    // 加载工具设置
    if (json.contains("tool")) {
        QString toolStr = json["tool"].toString();
        WhiteboardTypes::DrawingType tool = WhiteboardTypes::stringToDrawingType(toolStr);
        setCurrentTool(tool);
    }
    
    // 加载样式设置
    if (json.contains("penColor")) {
        setPenColor(QColor(json["penColor"].toString()));
    }
    
    if (json.contains("penWidth")) {
        setPenWidth(json["penWidth"].toDouble());
    }
    
    if (json.contains("brushColor")) {
        setBrushColor(QColor(json["brushColor"].toString()));
    }
    
    if (json.contains("antiAliasing")) {
        setAntiAliasing(json["antiAliasing"].toBool());
    }
    
    if (json.contains("multiTouchEnabled")) {
        setMultiTouchEnabled(json["multiTouchEnabled"].toBool());
    }
    
    emit sceneChanged();
    return true;
}

// 抗锯齿设置实现
void Whiteboard::setAntiAliasing(bool enabled)
{
    Q_D(Whiteboard);
    if (d->antiAliasing != enabled) {
        d->antiAliasing = enabled;

        // 通知工具管理器更新抗锯齿设置
        if (d->toolManager) {
            d->toolManager->setAntiAliasing(enabled);
        }

        // 通知视图更新抗锯齿设置
        if (d->view) {
            d->view->setAntiAliasing(enabled);
        }
    }
}

bool Whiteboard::antiAliasing() const
{
    Q_D(const Whiteboard);
    return d->antiAliasing;
}

// 多指绘制支持实现
void Whiteboard::setMultiTouchEnabled(bool enabled)
{
    Q_D(Whiteboard);
    if (d->multiTouchEnabled != enabled) {
        d->multiTouchEnabled = enabled;

        // 通知事件协调器更新多点触控设置
        if (d->eventCoordinator) {
            d->eventCoordinator->setMultiTouchEnabled(enabled);
        }

        // 控制视图的手势识别
        if (d->view) {
            if (enabled) {
                d->view->grabGesture(Qt::PinchGesture);
                d->view->grabGesture(Qt::PanGesture);
            } else {
                d->view->ungrabGesture(Qt::PinchGesture);
                d->view->ungrabGesture(Qt::PanGesture);
            }
        }
    }
}

bool Whiteboard::multiTouchEnabled() const
{
    Q_D(const Whiteboard);
    return d->multiTouchEnabled;
}

// 交互模式管理实现
void Whiteboard::setInteractionMode(InteractionMode mode)
{
    Q_D(Whiteboard);
    if (d->interactionMode != mode) {
        d->interactionMode = mode;

        // 通知事件协调器切换模式
        if (d->eventCoordinator) {
            EventCoordinator::EventMode coordinatorMode =
                (mode == InteractionMode::Drawing) ? EventCoordinator::EventMode::Drawing
                                                   : EventCoordinator::EventMode::Selection;
            d->eventCoordinator->setEventMode(coordinatorMode);
        }

        // 通知视图更新交互模式
        if (d->view) {
            bool drawingMode = (mode == InteractionMode::Drawing);
            d->view->setInteractionMode(drawingMode);
        }

        qDebug() << "Interaction mode changed to:" << static_cast<int>(mode);
    }
}

Whiteboard::InteractionMode Whiteboard::interactionMode() const
{
    Q_D(const Whiteboard);
    return d->interactionMode;
}

// 组件访问接口实现
WhiteboardView* Whiteboard::view() const
{
    Q_D(const Whiteboard);
    return d->view;
}

WhiteboardScene* Whiteboard::scene() const
{
    Q_D(const Whiteboard);
    return d->scene;
}

ToolManager* Whiteboard::toolManager() const
{
    Q_D(const Whiteboard);
    return d->toolManager;
}

SceneManager* Whiteboard::sceneManager() const
{
    Q_D(const Whiteboard);
    return d->sceneManager;
}

