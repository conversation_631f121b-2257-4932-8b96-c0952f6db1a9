23:11:19: Stopping process forcefully ....
23:11:19: The process was ended forcefully.

23:11:47: Starting C:\Users\<USER>\Desktop\project\qt\qt-graphicsview\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\qt-graphicsview.exe...
SceneManager initialized with expand margin: 1000
SceneManager signals connected to scene
SceneManager associated with scene: WhiteboardScene(0x1f2167ab140)
[绘制性能] 性能检测已启用
PenTool::PenTool - 性能分析器已启用
View transformed
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
Scene expanded to include rect: QRectF(-157,-150 1000x1000) -> new bounds: QRectF(-1157,-1150 3000x3000)
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
View transformed
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenTool::onInputPress - 开始绘制，输入ID: -1 位置: QPointF(-259,-213)
PenTool::createNewPath - 创建新路径，起始点: QPointF(-259,-213)
"[绘制性能] new PenPathItem 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first) 耗时: 0ms"
"[绘制性能] addItemToScene 耗时: 0ms"
PenTool::createNewPath - 路径创建并添加到场景完成
"[绘制性能] PenTool::createNewPath 耗时: 1ms"
"[绘制性能] PenTool::createNewPath 耗时: 1ms"
PenTool::onInputPress - 路径创建完成，活跃路径数: 1
"[绘制性能] PenTool::onInputPress 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
⚠️ 检测到卡顿! 距离上次move事件: 238 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-259,-215.225 151x94.225) 新边界: QRectF(-259,-215.225 155x104.225) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-272,-215.225 170.15x147.3) 新边界: QRectF(-279,-215.225 177.15x147.3) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-292.225,-215.225 190.375x147.3) 项边界: QRectF(-292.225,-215.225 190.375x147.3) 重绘面积: 28042.2 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-292.225,-238.075 384.225x170.15) 新边界: QRectF(-292.225,-238.075 403.225x170.15) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-304,-238.075 437.225x177.3) 新边界: QRectF(-332,-238.075 465.225x177.3) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 2ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
PenTool::onInputMove - 已处理 100 个移动事件 当前路径点数: 99 场景中图形项总数: 1
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] rebuildPath:"
"  调用次数: 99"
"  总耗时: 4ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] appendPoint:"
"  调用次数: 99"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 1"
"  总耗时: 1ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 100"
"  总耗时: 13ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] addItemToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] buildPath:"
"  调用次数: 99"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 99"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 98"
"  总耗时: 133ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 5ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 101"
"  总耗时: 9ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 167"
"  总耗时: 15ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] setPath:"
"  调用次数: 99"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] distanceCheck:"
"  调用次数: 101"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] calculateCurves:"
"  调用次数: 97"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-368.525,-238.075 520.525x177.3) 新边界: QRectF(-368.525,-238.075 534.525x177.3) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 100
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 90
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
PenTool::getOrCreateSegmentGroup - 创建新分段组，输入ID: -1
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 1 分段项地址: 0x1f2167ce840 组地址: 0x1f21b8be7b0
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 10
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-173,-199.075 339x103.075) 项边界: QRectF(-173,-199.075 339x103.075) 重绘面积: 34942.4 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 每秒调用次数: 124
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-227,-199.075 404.375x255.375) 新边界: QRectF(-242,-199.075 419.375x255.375) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-260.3,-199.075 437.675x255.375) 项边界: QRectF(-260.3,-199.075 437.675x255.375) 重绘面积: 111771 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-260.3,-199.075 437.675x255.375) 新边界: QRectF(-260.3,-199.075 437.675x255.375) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-361,-199.075 538.375x255.375) 新边界: QRectF(-372,-199.075 549.375x255.375) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-385.3,-199.075 562.675x255.375) 新边界: QRectF(-385.3,-199.075 562.675x255.375) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 100
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 90
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 2 分段项地址: 0x1f2167cd880 组地址: 0x1f21b8be7b0
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 10
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340.675,-95 324.675x134.3) 新边界: QRectF(-340.675,-113 324.675x152.3) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::onInputMove - 已处理 200 个移动事件 当前路径点数: 19 场景中图形项总数: 4
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] rebuildPath:"
"  调用次数: 199"
"  总耗时: 4ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] appendPoint:"
"  调用次数: 199"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 4"
"  总耗时: 4ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 1"
"  总耗时: 1ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] pathItem->points():"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 200"
"  总耗时: 20ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 4"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] resetPath:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] buildPath:"
"  调用次数: 203"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 203"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 298"
"  总耗时: 563ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 6ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 201"
"  总耗时: 14ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 267"
"  总耗时: 32ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 5ms"
"[绘制性能] setPath:"
"  调用次数: 203"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] distanceCheck:"
"  调用次数: 201"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] calculateCurves:"
"  调用次数: 201"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 200 暴露区域: QRectF(-368.525,-238.075 501.75x177.3) 项边界: QRectF(-368.525,-238.075 501.75x177.3) 重绘面积: 88960.3 项地址: 0x1f2167ce840
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340.675,-194.6 497.825x233.9) 新边界: QRectF(-340.675,-194.6 497.825x233.9) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 每秒调用次数: 235
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 72 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340.675,-206.875 497.825x246.175) 新边界: QRectF(-340.675,-206.875 497.825x246.175) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd880
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340.675,-223.9 554.675x263.2) 新边界: QRectF(-340.675,-223.9 580.675x263.2) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-368.525,-238.075 501.75x177.3) 项边界: QRectF(-368.525,-238.075 501.75x177.3) 重绘面积: 88960.3 项地址: 0x1f2167ce840
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-340.675,-223.9 591.05x263.2) 项边界: QRectF(-340.675,-223.9 591.05x263.2) 重绘面积: 155564 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340.675,-223.9 592.675x263.2) 新边界: QRectF(-340.675,-223.9 596.675x263.2) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::manageSegments - 创建分段，当前点数: 100
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 90
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 1ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 3 分段项地址: 0x1f2167ce8d0 组地址: 0x1f21b8be7b0
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 10
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 每秒调用次数: 184
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-220.375,-204.6 476.45x254.9) 新边界: QRectF(-220.375,-204.6 476.45x254.9) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd880
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::onInputMove - 已处理 300 个移动事件 当前路径点数: 29 场景中图形项总数: 5
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 3"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] rebuildPath:"
"  调用次数: 299"
"  总耗时: 6ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] appendPoint:"
"  调用次数: 299"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 6"
"  总耗时: 7ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 2ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 1"
"  总耗时: 1ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] pathItem->points():"
"  调用次数: 3"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 300"
"  总耗时: 32ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] resetPath:"
"  调用次数: 3"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup:"
"  调用次数: 3"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] buildPath:"
"  调用次数: 305"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 305"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 598"
"  总耗时: 1482ms"
"  平均耗时: 2ms"
"  最小耗时: 0ms"
"  最大耗时: 8ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 301"
"  总耗时: 21ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 367"
"  总耗时: 54ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 5ms"
"[绘制性能] setPath:"
"  调用次数: 305"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] distanceCheck:"
"  调用次数: 301"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] calculateCurves:"
"  调用次数: 303"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 9"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 3"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 3"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-220.375,-204.6 476.45x274.275) 项边界: QRectF(-220.375,-204.6 476.45x274.275) 重绘面积: 130678 项地址: 0x1f2167cd640
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-220.375,-204.6 480.375x274.275) 新边界: QRectF(-220.375,-204.6 503.375x274.275) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 2ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd880
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-220.375,-204.6 595.975x274.275) 新边界: QRectF(-220.375,-204.6 595.975x283.6) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
PenPathItem::paint - 每秒调用次数: 193
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 28 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-340.675,-223.9 591.05x263.2) 项边界: QRectF(-340.675,-223.9 591.05x263.2) 重绘面积: 155564 项地址: 0x1f2167ce8d0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-220.375,-204.6 595.975x358.9) 新边界: QRectF(-220.375,-204.6 595.975x358.9) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 13ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 13ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 13ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-368.525,-238.075 501.75x177.3) 项边界: QRectF(-368.525,-238.075 501.75x177.3) 重绘面积: 88960.3 项地址: 0x1f2167ce840
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 14ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 15ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 100
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 90
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 4 分段项地址: 0x1f2167cdc70 组地址: 0x1f21b8be7b0
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 10
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-219.375,-143.25 513.375x297.225) 新边界: QRectF(-219.375,-143.25 580.375x297.225) 项地址: 0x1f2167cd640
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-385.3,-199.075 562.675x255.375) 项边界: QRectF(-385.3,-199.075 562.675x255.375) 重绘面积: 143693 项地址: 0x1f2167cd880
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 每秒调用次数: 152
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint(final) 耗时: 0ms"
PenTool::onInputRelease - 绘制完成，最终点数: 18
PenTool::onInputRelease - 总点数: 382
PenTool::createFinalPath - 创建最终路径，总点数: 382
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] createFinalPathItem 耗时: 0ms"
"[绘制性能] addFinalPathToScene 耗时: 0ms"
PenTool::cleanupSegmentGroup - 清理分段组，输入ID: -1 组中项目数: 4
PenTool::cleanupSegmentGroup - 分段组已清理
"[绘制性能] PenTool::cleanupSegmentGroup 耗时: 0ms"
"[绘制性能] cleanupSegmentGroup 耗时: 0ms"
PenTool::createFinalPath - 最终路径创建完成
"[绘制性能] PenTool::createFinalPath 耗时: 1ms"
"[绘制性能] PenTool::createFinalPath 耗时: 1ms"
PenTool::finishPath - 完成路径绘制，最终点数: 382
PenTool::finishPath - 路径完成信号已发送
"[绘制性能] PenTool::finishPath 耗时: 0ms"
PenTool::onInputRelease - 所有绘制完成，退出绘制状态
"[绘制性能] PenTool::onInputRelease 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 36ms"
⚠️ 检测到卡顿! 距离上次move事件: 40 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
