⚠️ 检测到卡顿! 距离上次move事件: 3685 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenTool::onInputPress - 开始绘制，输入ID: -1 位置: QPointF(-234,-204)
PenTool::createNewPath - 创建新路径，起始点: QPointF(-234,-204)
"[绘制性能] new PenPathItem 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-234.5,-204.5 1x1) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first) 耗时: 1ms"
"[绘制性能] addItemToScene 耗时: 0ms"
PenTool::createNewPath - 路径创建并添加到场景完成
"[绘制性能] PenTool::createNewPath 耗时: 2ms"
"[绘制性能] PenTool::createNewPath 耗时: 2ms"
PenTool::onInputPress - 路径创建完成，活跃路径数: 1
"[绘制性能] PenTool::onInputPress 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
⚠️ 检测到卡顿! 距离上次move事件: 98 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.5,-204.5 1x1) 新边界: QRectF(-234,-205 0x1) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234,-205 0x1) 新边界: QRectF(-234.075,-206 1.075x2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-206 1.075x2) 新边界: QRectF(-234.075,-206.075 2.075x2.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-206.075 2.075x2.075) 新边界: QRectF(-234.075,-207 3.075x3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-207 3.075x3) 新边界: QRectF(-234.075,-208 7.075x4) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208 7.075x4) 新边界: QRectF(-234.075,-208.075 10.075x4.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.075 10.075x4.075) 新边界: QRectF(-234.075,-208.075 17.075x4.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.075 17.075x4.075) 新边界: QRectF(-234.075,-208.075 25.075x4.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.075 25.075x4.075) 新边界: QRectF(-234.075,-208.075 35.075x4.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-234.075,-208.075 35.075x4.075) 项边界: QRectF(-234.075,-208.075 35.075x4.075) 重绘面积: 142.931 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.075 35.075x4.075) 新边界: QRectF(-234.075,-208.3 45.075x4.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 45.075x4.3) 新边界: QRectF(-234.075,-208.3 54.075x11.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 54.075x11.3) 新边界: QRectF(-234.075,-208.3 62.075x18.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 62.075x18.3) 新边界: QRectF(-234.075,-208.3 68.075x27.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 68.075x27.3) 新边界: QRectF(-234.075,-208.3 72.075x39.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 72.075x39.3) 新边界: QRectF(-234.075,-208.3 77.075x51.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 77.075x51.3) 新边界: QRectF(-234.075,-208.3 80.075x64.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 80.075x64.3) 新边界: QRectF(-234.075,-208.3 81.075x78.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 81.075x78.3) 新边界: QRectF(-234.075,-208.3 82.075x91.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.075x91.3) 新边界: QRectF(-234.075,-208.3 82.15x103.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-234.075,-208.3 82.15x103.3) 项边界: QRectF(-234.075,-208.3 82.15x103.3) 重绘面积: 8486.09 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.15x103.3) 新边界: QRectF(-234.075,-208.3 82.15x115.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.15x115.3) 新边界: QRectF(-234.075,-208.3 82.225x128.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x128.3) 新边界: QRectF(-234.075,-208.3 82.225x139.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x139.3) 新边界: QRectF(-234.075,-208.3 82.225x147.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x147.3) 新边界: QRectF(-234.075,-208.3 82.225x155.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x155.3) 新边界: QRectF(-234.075,-208.3 82.225x160.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x160.3) 新边界: QRectF(-234.075,-208.3 82.225x163.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x163.3) 新边界: QRectF(-234.075,-208.3 82.225x166.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x166.3) 新边界: QRectF(-234.075,-208.3 82.225x167.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-234.075,-208.3 82.225x167.3) 新边界: QRectF(-243,-208.3 91.15x167.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-243,-208.3 91.15x167.375) 项边界: QRectF(-243,-208.3 91.15x167.375) 重绘面积: 15256.2 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-243,-208.3 91.15x167.375) 新边界: QRectF(-258,-208.3 106.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-258,-208.3 106.15x167.45) 新边界: QRectF(-270,-208.3 118.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-270,-208.3 118.15x167.45) 新边界: QRectF(-281,-208.3 129.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-281,-208.3 129.15x167.45) 新边界: QRectF(-287,-208.3 135.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-287,-208.3 135.15x167.45) 新边界: QRectF(-290,-208.3 138.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-290,-208.3 138.15x167.45) 新边界: QRectF(-292,-208.3 140.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-292,-208.3 140.15x167.45) 新边界: QRectF(-293,-208.3 141.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-293,-208.3 141.15x167.45) 新边界: QRectF(-294,-208.3 142.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294,-208.3 142.15x167.45) 新边界: QRectF(-294.075,-208.3 142.225x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.075,-208.3 142.225x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-294.15,-208.3 142.3x167.45) 项边界: QRectF(-294.15,-208.3 142.3x167.45) 重绘面积: 23828.1 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 142.3x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 142.3x167.45) 新边界: QRectF(-294.15,-208.3 158.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 158.15x167.45) 新边界: QRectF(-294.15,-208.3 191.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 191.15x167.45) 新边界: QRectF(-294.15,-208.3 218.15x167.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-294.15,-208.3 218.15x167.45) 项地址: 0x1efbe5c1d70
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 1ms"
"[绘制性能] PenPathItem::setPoints 耗时: 1ms"
"[绘制性能] PenPathItem::setPoints 耗时: 1ms"
"[绘制性能] addItemToScene(segment) 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 1ms"
"[绘制性能] createSegmentPath 耗时: 2ms"
PenTool::manageSegments - 分段已保存，输入ID: -1 总分段数: 1
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-294.15,-208.3 218.15x167.45) 新边界: QRectF(-136,-164 60x11) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 6ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 6ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-136,-164 60x11) 项边界: QRectF(-136,-164 60x11) 重绘面积: 660 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 60x11) 新边界: QRectF(-136,-164 81x21) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 81x21) 新边界: QRectF(-136,-164 97x32) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 97x32) 新边界: QRectF(-136,-164 105x39) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 105x39) 新边界: QRectF(-136,-164 113x54) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 113x54) 新边界: QRectF(-136,-164 119x71) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 119x71) 新边界: QRectF(-136,-164 121x89) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-136,-164 121x89) 项边界: QRectF(-136,-164 121x89) 重绘面积: 10769 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121x89) 新边界: QRectF(-136,-164 121.075x106) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x106) 新边界: QRectF(-136,-164 121.075x128) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x128) 新边界: QRectF(-136,-164 121.075x155) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x155) 新边界: QRectF(-136,-164 121.075x179) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x179) 新边界: QRectF(-136,-164 121.075x199) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-136,-164 121.075x199) 项边界: QRectF(-136,-164 121.075x199) 重绘面积: 24093.9 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x199) 新边界: QRectF(-136,-164 121.075x216) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-136,-164 121.075x216) 新边界: QRectF(-168,-164 153.075x225) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-168,-164 153.075x225) 新边界: QRectF(-212,-164 197.075x229) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-212,-164 197.075x229) 新边界: QRectF(-253,-164 238.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-253,-164 238.075x229.15) 新边界: QRectF(-293,-164 278.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-293,-164 278.075x229.15) 项边界: QRectF(-293,-164 278.075x229.15) 重绘面积: 63720.9 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-293,-164 278.075x229.15) 新边界: QRectF(-326,-164 311.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-326,-164 311.075x229.15) 新边界: QRectF(-343,-164 328.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-343,-164 328.075x229.15) 新边界: QRectF(-358,-164 343.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-358,-164 343.075x229.15) 新边界: QRectF(-372,-164 357.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-372,-164 357.075x229.15) 新边界: QRectF(-378,-164 363.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-378,-164 363.075x229.15) 项边界: QRectF(-378,-164 363.075x229.15) 重绘面积: 83198.6 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-378,-164 363.075x229.15) 新边界: QRectF(-381,-164 366.075x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381,-164 366.075x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-381.225,-164 366.3x229.15) 项边界: QRectF(-381.225,-164 366.3x229.15) 重绘面积: 83937.6 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-381.225,-164 366.3x229.15) 项边界: QRectF(-381.225,-164 366.3x229.15) 重绘面积: 83937.6 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-381.225,-164 366.3x229.15) 项边界: QRectF(-381.225,-164 366.3x229.15) 重绘面积: 83937.6 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 366.3x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 366.3x229.15) 新边界: QRectF(-381.225,-164 370.225x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 370.225x229.15) 新边界: QRectF(-381.225,-164 382.225x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 382.225x229.15) 新边界: QRectF(-381.225,-164 392.225x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 392.225x229.15) 新边界: QRectF(-381.225,-164 397.225x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-381.225,-164 397.225x229.15) 项边界: QRectF(-381.225,-164 397.225x229.15) 重绘面积: 91024.1 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.225x229.15) 新边界: QRectF(-381.225,-164 397.6x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.6x229.15) 新边界: QRectF(-381.225,-164 397.675x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.675x229.15) 新边界: QRectF(-381.225,-164 397.675x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.675x229.15) 新边界: QRectF(-381.225,-164 397.675x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.675x229.15) 新边界: QRectF(-381.225,-164 397.675x229.15) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 140 暴露区域: QRectF(-381.225,-164 397.675x229.15) 项边界: QRectF(-381.225,-164 397.675x229.15) 重绘面积: 91127.2 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.675x229.15) 新边界: QRectF(-381.225,-164 397.675x230) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-381.225,-164 397.675x230) 项地址: 0x1efbe5c2550
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] addItemToScene(segment) 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
PenTool::manageSegments - 分段已保存，输入ID: -1 总分段数: 2
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-381.225,-164 397.675x230) 新边界: QRectF(-56,39 42x27) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-56,39 42x27) 新边界: QRectF(-117,39 103x41) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-117,39 103x41) 新边界: QRectF(-158,39 144x42.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-158,39 144x42.05) 新边界: QRectF(-200,39 186x42.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::onInputMove - 已处理 100 个移动事件 当前路径点数: 6 场景中图形项总数: 3
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 2"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] buildPath:"
"  调用次数: 104"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] rebuildPath:"
"  调用次数: 100"
"  总耗时: 32ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 4"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 1"
"  总耗时: 3ms"
"  平均耗时: 3ms"
"  最小耗时: 3ms"
"  最大耗时: 3ms"
"[绘制性能] distanceCheck:"
"  调用次数: 101"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 101"
"  总耗时: 69ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] calculateCurves:"
"  调用次数: 102"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 2"
"  总耗时: 4ms"
"  平均耗时: 2ms"
"  最小耗时: 2ms"
"  最大耗时: 2ms"
"[绘制性能] resetPath:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 100"
"  总耗时: 78ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 104"
"  总耗时: 20ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 1"
"  总耗时: 1ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene(segment):"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 6"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] setPath:"
"  调用次数: 104"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 4"
"  总耗时: 8ms"
"  平均耗时: 2ms"
"  最小耗时: 1ms"
"  最大耗时: 3ms"
"[绘制性能] appendPoint:"
"  调用次数: 100"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 231"
"  总耗时: 98ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 6ms"
"[绘制性能] ToolManager::handleInputMove:"
"  调用次数: 231"
"  总耗时: 110ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 6ms"
"[绘制性能] pathItem->points():"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 148"
"  总耗时: 165ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 4ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 12ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 13ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-381.225,-164 397.675x230) 项边界: QRectF(-381.225,-164 397.675x230) 重绘面积: 91465.3 项地址: 0x1efbe5c2550
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-200,39 186x42.05) 新边界: QRectF(-282,39 268x42.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-282,39 268x42.05) 新边界: QRectF(-314,30 300x51.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-314,30 300x51.05) 新边界: QRectF(-339,12 325x69.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-339,12 325x69.05) 新边界: QRectF(-355,-6 341x87.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-355,-6 341x87.05) 新边界: QRectF(-364,-21 350x102.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
PenPathItem::paint - 调用# 160 暴露区域: QRectF(-381.225,-164 397.675x230) 项边界: QRectF(-381.225,-164 397.675x230) 重绘面积: 91465.3 项地址: 0x1efbe5c2550
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-364,-21 350x102.05) 新边界: QRectF(-369,-36 355x117.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-369,-36 355x117.05) 新边界: QRectF(-370,-49 356x130.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370,-49 356x130.05) 新边界: QRectF(-370.075,-65 356.075x146.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-65 356.075x146.05) 新边界: QRectF(-370.075,-80 356.075x161.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 170 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-80 356.075x161.05) 新边界: QRectF(-370.075,-97 356.075x178.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-97 356.075x178.05) 新边界: QRectF(-370.075,-114 356.075x195.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-114 356.075x195.05) 新边界: QRectF(-370.075,-131 356.075x212.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-131 356.075x212.05) 新边界: QRectF(-370.075,-144 356.075x225.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 180 暴露区域: QRectF(-381.225,-164 397.675x230) 项边界: QRectF(-381.225,-164 397.675x230) 重绘面积: 91465.3 项地址: 0x1efbe5c2550
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-144 356.075x225.05) 新边界: QRectF(-370.075,-151 356.075x232.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-151 356.075x232.05) 新边界: QRectF(-370.075,-153 356.075x234.05) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153 356.075x234.05) 新边界: QRectF(-370.075,-153.675 356.075x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 每秒调用次数: 189
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 356.075x234.725) 新边界: QRectF(-370.075,-153.675 409.075x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 409.075x234.725) 新边界: QRectF(-370.075,-153.675 450.075x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 450.075x234.725) 新边界: QRectF(-370.075,-153.675 468.075x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 468.075x234.725) 新边界: QRectF(-370.075,-153.675 470.075x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-370.075,-153.675 470.075x234.725) 项边界: QRectF(-370.075,-153.675 470.075x234.725) 重绘面积: 110338 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.075x234.725) 新边界: QRectF(-370.075,-153.675 470.225x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x234.725) 新边界: QRectF(-370.075,-153.675 470.225x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x234.725) 新边界: QRectF(-370.075,-153.675 470.225x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x234.725) 新边界: QRectF(-370.075,-153.675 470.225x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x234.725) 新边界: QRectF(-370.075,-153.675 470.225x234.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x234.725) 新边界: QRectF(-370.075,-153.675 470.225x238.675) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x238.675) 新边界: QRectF(-370.075,-153.675 470.225x240.675) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-381.225,-164 397.675x230) 项边界: QRectF(-381.225,-164 397.675x230) 重绘面积: 91465.3 项地址: 0x1efbe5c2550
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x240.675) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 3ms"
"[绘制性能] PenTool::addPoint 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-370.075,-153.675 470.225x241.125) 项边界: QRectF(-370.075,-153.675 470.225x241.125) 重绘面积: 113383 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-153.675 470.225x241.125) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-153.675 470.225x241.125) 新边界: QRectF(-370.075,-181 470.225x268.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-181 470.225x268.45) 新边界: QRectF(-370.075,-199 470.225x286.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-199 470.225x286.45) 新边界: QRectF(-370.075,-200.275 470.225x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-381.225,-164 397.675x230) 项边界: QRectF(-381.225,-164 397.675x230) 重绘面积: 91465.3 项地址: 0x1efbe5c2550
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 470.225x287.725) 新边界: QRectF(-370.075,-200.275 470.225x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 470.225x287.725) 新边界: QRectF(-370.075,-200.275 470.225x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 470.225x287.725) 新边界: QRectF(-370.075,-200.275 473.075x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 473.075x287.725) 新边界: QRectF(-370.075,-200.275 476.075x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-370.075,-200.275 476.075x287.725) 项边界: QRectF(-370.075,-200.275 476.075x287.725) 重绘面积: 136979 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 476.075x287.725) 新边界: QRectF(-370.075,-200.275 478.7x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 478.7x287.725) 新边界: QRectF(-370.075,-200.275 478.7x287.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-370.075,-200.275 478.7x287.725) 项地址: 0x1efbe5c21f0
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 1ms"
"[绘制性能] PenPathItem::setPoints 耗时: 1ms"
"[绘制性能] addItemToScene(segment) 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 1ms"
"[绘制性能] createSegmentPath 耗时: 1ms"
PenTool::manageSegments - 分段已保存，输入ID: -1 总分段数: 3
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-370.075,-200.275 478.7x287.725) 新边界: QRectF(50,-119 56x75) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 4ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(50,-119 56x75) 新边界: QRectF(-72,-119 178x118) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-72,-119 178x118) 项边界: QRectF(-72,-119 178x118) 重绘面积: 21004 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-72,-119 178x118) 新边界: QRectF(-112,-119 218x123) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-112,-119 218x123) 新边界: QRectF(-158,-119 264x127) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-158,-119 264x127) 新边界: QRectF(-204,-119 310x127.3) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-204,-119 310x127.3) 新边界: QRectF(-249,-119 355x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-249,-119 355x127.375) 新边界: QRectF(-289,-119 395x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-289,-119 395x127.375) 新边界: QRectF(-320,-119 426x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-320,-119 426x127.375) 新边界: QRectF(-354,-119 460x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-354,-119 460x127.375) 新边界: QRectF(-359,-119 465x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-359,-119 465x127.375) 新边界: QRectF(-360,-119 466x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360,-119 466x127.375) 新边界: QRectF(-360.075,-119 466.075x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-119 466.075x127.375) 新边界: QRectF(-360.075,-119 466.075x127.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-119 466.075x127.375) 新边界: QRectF(-360.075,-130 466.075x138.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-130 466.075x138.375) 新边界: QRectF(-360.075,-169 466.075x177.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-169 466.075x177.375) 新边界: QRectF(-360.075,-177 466.075x185.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177 466.075x185.375) 新边界: QRectF(-360.075,-177.075 466.075x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 466.075x185.45) 新边界: QRectF(-360.075,-177.075 466.075x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 466.075x185.45) 新边界: QRectF(-360.075,-177.075 472.075x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 472.075x185.45) 新边界: QRectF(-360.075,-177.075 490.075x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 140 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 490.075x185.45) 新边界: QRectF(-360.075,-177.075 492.075x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.075x185.45) 新边界: QRectF(-360.075,-177.075 492.3x185.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x185.45) 新边界: QRectF(-360.075,-177.075 492.3x192.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x192.075) 新边界: QRectF(-360.075,-177.075 492.3x223.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x223.075) 新边界: QRectF(-360.075,-177.075 492.3x236.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 160 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x236.075) 新边界: QRectF(-360.075,-177.075 492.3x246.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.075) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 170 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 180 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 190 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 200 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 210 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 492.3x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 492.3x246.45) 新边界: QRectF(-360.075,-177.075 547.075x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 220 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 547.075x246.45) 新边界: QRectF(-360.075,-177.075 564.075x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 564.075x246.45) 新边界: QRectF(-360.075,-177.075 581.075x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 230 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 581.075x246.45) 新边界: QRectF(-360.075,-177.075 584.075x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 584.075x246.45) 新边界: QRectF(-360.075,-177.075 584.9x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 584.9x246.45) 新边界: QRectF(-360.075,-177.075 584.9x246.45) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
PenPathItem::paint - 调用# 240 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 584.9x246.45) 新边界: QRectF(-360.075,-177.075 584.9x263.075) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-360.075,-177.075 584.9x263.075) 项地址: 0x1efbe5c1590
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] addItemToScene(segment) 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 1ms"
PenTool::manageSegments - 分段已保存，输入ID: -1 总分段数: 4
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-360.075,-177.075 584.9x263.075) 新边界: QRectF(75,12 135x74) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 每秒调用次数: 246
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(75,12 135x74) 新边界: QRectF(-44,12 254x86) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-44,12 254x86) 新边界: QRectF(-174,12 384x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-174,12 384x86.375) 新边界: QRectF(-232,12 442x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-232,12 442x86.375) 项边界: QRectF(-232,12 442x86.375) 重绘面积: 38177.8 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232,12 442x86.375) 新边界: QRectF(-264,12 474x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-264,12 474x86.375) 新边界: QRectF(-292,12 502x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-292,12 502x86.375) 新边界: QRectF(-316,12 526x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-316,12 526x86.375) 新边界: QRectF(-325,12 535x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-325,12 535x86.375) 新边界: QRectF(-329,12 539x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-329,12 539x86.375) 项边界: QRectF(-329,12 539x86.375) 重绘面积: 46556.1 项地址: 0x1efbe5c1c50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-329,12 539x86.375) 新边界: QRectF(-332,12 542x86.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::onInputMove - 已处理 200 个移动事件 当前路径点数: 12 场景中图形项总数: 5
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 4"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] buildPath:"
"  调用次数: 208"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] rebuildPath:"
"  调用次数: 200"
"  总耗时: 48ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 8"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 1"
"  总耗时: 3ms"
"  平均耗时: 3ms"
"  最小耗时: 3ms"
"  最大耗时: 3ms"
"[绘制性能] distanceCheck:"
"  调用次数: 201"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 201"
"  总耗时: 116ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 3ms"
"[绘制性能] calculateCurves:"
"  调用次数: 206"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 2"
"  总耗时: 4ms"
"  平均耗时: 2ms"
"  最小耗时: 2ms"
"  最大耗时: 2ms"
"[绘制性能] resetPath:"
"  调用次数: 4"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 200"
"  总耗时: 138ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 3ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 208"
"  总耗时: 31ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 1"
"  总耗时: 1ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 4"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene(segment):"
"  调用次数: 4"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 4"
"  总耗时: 4ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 12"
"  总耗时: 4ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] setPath:"
"  调用次数: 208"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 8"
"  总耗时: 16ms"
"  平均耗时: 2ms"
"  最小耗时: 1ms"
"  最大耗时: 3ms"
"[绘制性能] appendPoint:"
"  调用次数: 200"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 331"
"  总耗时: 183ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 12ms"
"[绘制性能] ToolManager::handleInputMove:"
"  调用次数: 331"
"  总耗时: 204ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 13ms"
"[绘制性能] pathItem->points():"
"  调用次数: 4"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 468"
"  总耗时: 739ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 5ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 11ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332,12 542x86.375) 新边界: QRectF(-332.15,-19 542.15x117.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-19 542.15x117.375) 新边界: QRectF(-332.15,-37 542.15x135.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-37 542.15x135.375) 新边界: QRectF(-332.15,-73 542.15x171.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-73 542.15x171.375) 新边界: QRectF(-332.15,-92 542.15x190.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-92 542.15x190.375) 新边界: QRectF(-332.15,-127 542.15x225.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-127 542.15x225.375) 新边界: QRectF(-332.15,-141 542.15x239.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-141 542.15x239.375) 新边界: QRectF(-332.15,-158 542.15x256.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-158 542.15x256.375) 新边界: QRectF(-332.15,-159.35 542.15x257.725) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-159.35 542.15x257.725) 新边界: QRectF(-332.15,-160.275 565.15x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 565.15x258.65) 新边界: QRectF(-332.15,-160.275 604.15x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 604.15x258.65) 新边界: QRectF(-332.15,-160.275 619.15x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 619.15x258.65) 新边界: QRectF(-332.15,-160.275 620.125x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x258.65) 新边界: QRectF(-332.15,-160.275 620.125x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x258.65) 新边界: QRectF(-332.15,-160.275 620.125x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x258.65) 新边界: QRectF(-332.15,-160.275 620.125x258.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x258.65) 新边界: QRectF(-332.15,-160.275 620.125x267.275) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x267.275) 新边界: QRectF(-332.15,-160.275 620.125x276.275) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x276.275) 新边界: QRectF(-332.15,-160.275 620.125x276.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-332.15,-160.275 620.125x276.65) 新边界: QRectF(-374,-160.275 661.975x276.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 2ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-374,-160.275 661.975x276.65) 新边界: QRectF(-396,-160.275 683.975x276.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 32 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396,-160.275 683.975x276.65) 新边界: QRectF(-396.9,-160.275 684.875x276.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-160.275 684.875x276.65) 新边界: QRectF(-396.9,-160.275 684.875x276.65) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-160.275 684.875x276.65) 新边界: QRectF(-396.9,-166 684.875x282.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 140 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 33 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-166 684.875x282.375) 新边界: QRectF(-396.9,-199 684.875x315.375) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 40 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199 684.875x315.375) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 35 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
⚠️ 检测到卡顿! 距离上次move事件: 44 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 160 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 43 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 40 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 170 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 38 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 38 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 180 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 36 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 43 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 190 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 43 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 13ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 44 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-396.9,-199.825 684.875x316.2) 项地址: 0x1efbe5c1e90
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] addItemToScene(segment) 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 1ms"
"[绘制性能] createSegmentPath 耗时: 1ms"
PenTool::manageSegments - 分段已保存，输入ID: -1 总分段数: 5
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-396.9,-199.825 684.875x316.2) 新边界: QRectF(-82,34 276x58) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::manageSegments 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 4ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 200 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
⚠️ 检测到卡顿! 距离上次move事件: 39 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-82,34 276x58) 新边界: QRectF(-315,1 509x94.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
PenPathItem::paint - 每秒调用次数: 203
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-315,1 509x94.975) 新边界: QRectF(-333.75,-62 527.75x157.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-396.9,-199.825 684.875x316.2) 项边界: QRectF(-396.9,-199.825 684.875x316.2) 重绘面积: 216557 项地址: 0x1efbe5c1e90
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-62 527.75x157.975) 新边界: QRectF(-333.75,-96 527.75x191.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-96 527.75x191.975) 新边界: QRectF(-333.75,-116 527.75x211.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-116 527.75x211.975) 新边界: QRectF(-333.75,-154 527.75x249.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-154 527.75x249.975) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-396.9,-199.825 684.875x316.2) 项边界: QRectF(-396.9,-199.825 684.875x316.2) 重绘面积: 216557 项地址: 0x1efbe5c1e90
"[绘制性能] PenPathItem::paint 耗时: 8ms"
⚠️ 检测到卡顿! 距离上次move事件: 33 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 37 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-396.9,-199.825 684.875x316.2) 项边界: QRectF(-396.9,-199.825 684.875x316.2) 重绘面积: 216557 项地址: 0x1efbe5c1e90
"[绘制性能] PenPathItem::paint 耗时: 7ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-156.625 527.75x252.6) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 38 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-156.625 527.75x252.6) 新边界: QRectF(-333.75,-171 527.75x266.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
⚠️ 检测到卡顿! 距离上次move事件: 35 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-171 527.75x266.975) 新边界: QRectF(-333.75,-218 527.75x313.975) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 37 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218 527.75x313.975) 新边界: QRectF(-333.75,-218.225 583.75x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
⚠️ 检测到卡顿! 距离上次move事件: 50 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 583.75x314.2) 新边界: QRectF(-333.75,-218.225 605.75x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-396.9,-199.825 684.875x316.2) 项边界: QRectF(-396.9,-199.825 684.875x316.2) 重绘面积: 216557 项地址: 0x1efbe5c1e90
"[绘制性能] PenPathItem::paint 耗时: 13ms"
⚠️ 检测到卡顿! 距离上次move事件: 47 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 605.75x314.2) 新边界: QRectF(-333.75,-218.225 613.1x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 13ms"
⚠️ 检测到卡顿! 距离上次move事件: 42 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 613.1x314.2) 新边界: QRectF(-333.75,-218.225 613.1x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-370.075,-200.275 478.7x287.725) 项边界: QRectF(-370.075,-200.275 478.7x287.725) 重绘面积: 137734 项地址: 0x1efbe5c21f0
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 44 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 613.1x314.2) 新边界: QRectF(-333.75,-218.225 613.1x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 613.1x314.2) 新边界: QRectF(-333.75,-218.225 613.1x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
⚠️ 检测到卡顿! 距离上次move事件: 52 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-333.75,-218.225 613.1x314.2) 新边界: QRectF(-333.75,-218.225 613.1x314.2) 项地址: 0x1efbe5c1c50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint(final) 耗时: 0ms"
PenTool::onInputRelease - 绘制完成，最终点数: 28
PenTool::finishPath - 完成路径绘制，最终点数: 28
PenTool::finishPath - 路径完成信号已发送
"[绘制性能] PenTool::finishPath 耗时: 0ms"
PenTool::onInputRelease - 所有绘制完成，退出绘制状态
"[绘制性能] PenTool::onInputRelease 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-294.15,-208.3 218.15x167.45) 项边界: QRectF(-294.15,-208.3 218.15x167.45) 重绘面积: 36529.2 项地址: 0x1efbe5c1d70
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
⚠️ 检测到卡顿! 距离上次move事件: 38 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] ToolManager::handleInputMove 耗时: 0ms"
