⚠️ 检测到卡顿! 距离上次move事件: 6817 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenTool::onInputPress - 开始绘制，输入ID: -1 位置: QPointF(-188,-158)
PenTool::createNewPath - 创建新路径，起始点: QPointF(-188,-158)
"[绘制性能] new PenPathItem 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-188.5,-158.5 1x1) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first) 耗时: 0ms"
"[绘制性能] addItemToScene 耗时: 0ms"
PenTool::createNewPath - 路径创建并添加到场景完成
"[绘制性能] PenTool::createNewPath 耗时: 0ms"
"[绘制性能] PenTool::createNewPath 耗时: 0ms"
PenTool::onInputPress - 路径创建完成，活跃路径数: 1
"[绘制性能] PenTool::onInputPress 耗时: 0ms"
PenPathItem::paint - 每秒调用次数: 16
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
⚠️ 检测到卡顿! 距离上次move事件: 112 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188.5,-158.5 1x1) 新边界: QRectF(-188,-159 1x1) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-159 1x1) 新边界: QRectF(-188,-161 4x3) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-161 4x3) 新边界: QRectF(-188,-161.15 5x3.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-161.15 5x3.15) 新边界: QRectF(-188,-162 8x4) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-162 8x4) 新边界: QRectF(-188,-165 14x7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-165 14x7) 新边界: QRectF(-188,-169 22x11) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-169 22x11) 新边界: QRectF(-188,-172 35x14) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-172 35x14) 新边界: QRectF(-188,-175 50x17) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-175 50x17) 新边界: QRectF(-188,-181 87x23) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-181 87x23) 新边界: QRectF(-188,-185 124x27) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-185 124x27) 新边界: QRectF(-188,-187 162x29) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187 162x29) 新边界: QRectF(-188,-187.15 200x29.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 200x29.15) 新边界: QRectF(-188,-187.15 239x29.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 239x29.15) 新边界: QRectF(-188,-187.15 265x29.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 265x29.15) 新边界: QRectF(-188,-187.15 312x29.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 312x29.15) 新边界: QRectF(-188,-187.15 340x36.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 340x36.15) 新边界: QRectF(-188,-187.15 369x54.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 369x54.15) 新边界: QRectF(-188,-187.15 394x72.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 394x72.15) 新边界: QRectF(-188,-187.15 415x90.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 415x90.15) 新边界: QRectF(-188,-187.15 428x110.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 428x110.15) 新边界: QRectF(-188,-187.15 436x125.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 436x125.15) 新边界: QRectF(-188,-187.15 439x138.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 439x138.15) 新边界: QRectF(-188,-187.15 440x150.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x150.15) 新边界: QRectF(-188,-187.15 440x162.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x162.15) 新边界: QRectF(-188,-187.15 440x171.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x171.15) 新边界: QRectF(-188,-187.15 440x188.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x188.15) 新边界: QRectF(-188,-187.15 440x195.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x195.15) 新边界: QRectF(-188,-187.15 440x207.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x207.15) 新边界: QRectF(-188,-187.15 440x209.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.15) 新边界: QRectF(-188,-187.15 440x209.3) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.3) 新边界: QRectF(-188,-187.15 440x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.375) 新边界: QRectF(-188,-187.15 440x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.375) 新边界: QRectF(-188,-187.15 440x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.375) 新边界: QRectF(-188,-187.15 440x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-188,-187.15 440x209.375) 新边界: QRectF(-208,-187.15 460x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-208,-187.15 460x209.375) 新边界: QRectF(-211,-187.15 463x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211,-187.15 463x209.375) 新边界: QRectF(-211.075,-187.15 463.075x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-187.15 463.075x209.375) 新边界: QRectF(-211.075,-187.15 463.075x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-187.15 463.075x209.375) 新边界: QRectF(-211.075,-187.15 463.075x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-187.15 463.075x209.375) 新边界: QRectF(-211.075,-187.15 463.075x209.375) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-187.15 463.075x209.375) 新边界: QRectF(-211.075,-200 463.075x222.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-200 463.075x222.225) 新边界: QRectF(-211.075,-211 463.075x233.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211 463.075x233.225) 新边界: QRectF(-211.075,-211.75 463.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 463.075x233.975) 新边界: QRectF(-211.075,-211.75 463.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 463.075x233.975) 新边界: QRectF(-211.075,-211.75 463.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 463.075x233.975) 新边界: QRectF(-211.075,-211.75 483.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 483.075x233.975) 新边界: QRectF(-211.075,-211.75 509.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 509.075x233.975) 新边界: QRectF(-211.075,-211.75 515.075x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 515.075x233.975) 新边界: QRectF(-211.075,-211.75 515.525x233.975) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-211.075,-211.75 515.525x233.975) 项地址: 0x23a20bc6d50
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
PenTool::getOrCreateSegmentGroup - 创建新分段组，输入ID: -1
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 1 分段项地址: 0x23a20bc6d50 组地址: 0x23a22d40550
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-211.075,-211.75 515.525x233.975) 新边界: QRectF(298,-126 6.45x35) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(298,-126 6.45x35) 新边界: QRectF(298,-126 6.45x45) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(298,-126 6.45x45) 新边界: QRectF(280,-126 24.45x70) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(280,-126 24.45x70) 新边界: QRectF(237,-126 67.45x100) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::onInputMove - 已处理 300 个移动事件 当前路径点数: 6 场景中图形项总数: 4
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] pathItem->points():"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 437"
"  总耗时: 123ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 5ms"
"[绘制性能] PenTool::cleanupSegmentGroup:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 12"
"  总耗时: 12ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 13"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup:"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 2"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::addPoint(final):"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 300"
"  总耗时: 86ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] buildPath:"
"  调用次数: 314"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createFinalPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 698"
"  总耗时: 526ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 12ms"
"[绘制性能] PenTool::onInputRelease:"
"  调用次数: 1"
"  总耗时: 2ms"
"  平均耗时: 2ms"
"  最小耗时: 2ms"
"  最大耗时: 2ms"
"[绘制性能] calculateCurves:"
"  调用次数: 310"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] distanceCheck:"
"  调用次数: 303"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] appendPoint:"
"  调用次数: 301"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createFinalPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 19"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] resetPath:"
"  调用次数: 6"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] rebuildPath:"
"  调用次数: 301"
"  总耗时: 19ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::finishPath:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 4"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] cleanupSegmentGroup:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 6"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 314"
"  总耗时: 13ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] addFinalPathToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] setPath:"
"  调用次数: 314"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 303"
"  总耗时: 72ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(237,-126 67.45x100) 新边界: QRectF(201,-126 103.45x113) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(201,-126 103.45x113) 新边界: QRectF(158,-126 146.45x124) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(158,-126 146.45x124) 新边界: QRectF(105,-126 199.45x132) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(105,-126 199.45x132) 新边界: QRectF(48,-126 256.45x137) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(48,-126 256.45x137) 新边界: QRectF(-77,-126 381.45x140) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-77,-126 381.45x140) 项边界: QRectF(-77,-126 381.45x140) 重绘面积: 53403 项地址: 0x23a20bc6840
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-77,-126 381.45x140) 新边界: QRectF(-137,-126 441.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-137,-126 441.45x140.075) 新边界: QRectF(-192,-126 496.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-192,-126 496.45x140.075) 新边界: QRectF(-240,-126 544.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-240,-126 544.45x140.075) 新边界: QRectF(-279,-126 583.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-279,-126 583.45x140.075) 新边界: QRectF(-316,-126 620.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-316,-126 620.45x140.075) 新边界: QRectF(-340,-126 644.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-340,-126 644.45x140.075) 新边界: QRectF(-343,-126 647.45x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-343,-126 647.45x140.075) 新边界: QRectF(-343.225,-126 647.675x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-343.225,-126 647.675x140.075) 新边界: QRectF(-344.725,-126 649.175x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-126 649.175x140.075) 新边界: QRectF(-344.725,-133 649.175x147.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-133 649.175x147.075) 新边界: QRectF(-344.725,-174 649.175x188.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
PenPathItem::paint - 调用# 140 暴露区域: QRectF(-344.725,-174 649.175x188.075) 项边界: QRectF(-344.725,-174 649.175x188.075) 重绘面积: 122094 项地址: 0x23a20bc6840
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-174 649.175x188.075) 新边界: QRectF(-344.725,-192 649.175x206.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-192 649.175x206.075) 新边界: QRectF(-344.725,-212 649.175x226.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-212 649.175x226.075) 新边界: QRectF(-344.725,-216 649.175x230.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216 649.175x230.075) 新边界: QRectF(-344.725,-216.15 649.175x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 649.175x230.225) 新边界: QRectF(-344.725,-216.15 649.175x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 649.175x230.225) 新边界: QRectF(-344.725,-216.15 649.175x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 649.175x230.225) 新边界: QRectF(-344.725,-216.15 650.725x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 1ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 2ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 650.725x230.225) 新边界: QRectF(-344.725,-216.15 664.725x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 28 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 664.725x230.225) 新边界: QRectF(-344.725,-216.15 678.725x230.225) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 160 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 678.725x230.225) 新边界: QRectF(-344.725,-216.15 680.6x250.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x250.15) 新边界: QRectF(-344.725,-216.15 680.6x304.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x304.15) 新边界: QRectF(-344.725,-216.15 680.6x326.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
PenPathItem::paint - 调用# 170 暴露区域: QRectF(-344.725,-216.15 680.6x326.15) 项边界: QRectF(-344.725,-216.15 680.6x326.15) 重绘面积: 221978 项地址: 0x23a20bc6840
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x326.15) 新边界: QRectF(-344.725,-216.15 680.6x328.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
PenPathItem::paint - 每秒调用次数: 173
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x328.15) 新边界: QRectF(-344.725,-216.15 680.6x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x329.5) 新边界: QRectF(-344.725,-216.15 680.6x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-344.725,-216.15 680.6x329.5) 新边界: QRectF(-348,-216.15 683.875x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 2ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-348,-216.15 683.875x329.5) 新边界: QRectF(-352,-216.15 687.875x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-352,-216.15 687.875x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-353.8,-216.15 689.675x329.5) 项边界: QRectF(-353.8,-216.15 689.675x329.5) 重绘面积: 227248 项地址: 0x23a20bc6840
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 20 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x329.5) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x329.5) 新边界: QRectF(-353.8,-216.15 689.675x375.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x375.15) 新边界: QRectF(-353.8,-216.15 689.675x391.15) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-353.8,-216.15 689.675x391.15) 项地址: 0x23a20bc6de0
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 2 分段项地址: 0x23a20bc6de0 组地址: 0x23a22d40550
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-353.8,-216.15 689.675x391.15) 新边界: QRectF(-158,109 346x66) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 2ms"
"[绘制性能] PenTool::manageSegments 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-158,109 346x66) 新边界: QRectF(-313,109 501x66.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-353.8,-216.15 689.675x391.15) 项边界: QRectF(-353.8,-216.15 689.675x391.15) 重绘面积: 269766 项地址: 0x23a20bc6de0
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-313,109 501x66.075) 新边界: QRectF(-380,109 568x66.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-380,109 568x66.075) 新边界: QRectF(-397,104 585x71.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-397,104 585x71.075) 新边界: QRectF(-400,89 588x86.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400,89 588x86.075) 新边界: QRectF(-400.075,63 588.075x112.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,63 588.075x112.075) 新边界: QRectF(-400.075,35 588.075x140.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,35 588.075x140.075) 新边界: QRectF(-400.075,-25 588.075x200.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-25 588.075x200.075) 新边界: QRectF(-400.075,-57 588.075x232.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-57 588.075x232.075) 新边界: QRectF(-400.075,-114 588.075x289.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-114 588.075x289.075) 新边界: QRectF(-400.075,-135 588.075x310.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-135 588.075x310.075) 新边界: QRectF(-400.075,-157 588.075x332.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 18 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-157 588.075x332.075) 新边界: QRectF(-400.075,-162 588.075x337.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-162 588.075x337.075) 新边界: QRectF(-400.075,-163.2 635.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 635.075x338.275) 新边界: QRectF(-400.075,-163.2 742.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 742.075x338.275) 新边界: QRectF(-400.075,-163.2 773.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 773.075x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 19 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 21 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 774.2x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 调用# 140 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 774.2x338.275) 新边界: QRectF(-400.075,-163.2 775.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 775.075x338.275) 新边界: QRectF(-400.075,-163.2 828.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 828.075x338.275) 新边界: QRectF(-400.075,-163.2 833.075x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 150 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 833.075x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 160 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
PenPathItem::paint - 每秒调用次数: 164
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 37 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 3ms"
"[绘制性能] PenTool::addPoint 耗时: 3ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 32 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 10 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 28 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-163.2 835.175x338.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
PenPathItem::paint - 调用# 20 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-163.2 835.175x338.275) 新边界: QRectF(-400.075,-175 835.175x350.075) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175 835.175x350.075) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 30 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
PenPathItem::paint - 调用# 30 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 32 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
PenPathItem::paint - 调用# 40 暴露区域: QRectF(-211.075,-211.75 515.525x233.975) 项边界: QRectF(-211.075,-211.75 515.525x233.975) 重绘面积: 120620 项地址: 0x23a20bc6d50
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 6ms"
⚠️ 检测到卡顿! 距离上次move事件: 36 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 1ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 1ms"
"[绘制性能] rebuildPath 耗时: 1ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 7ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 48 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::manageSegments - 创建分段，当前点数: 50
"[绘制性能] pathItem->points() 耗时: 0ms"
PenTool::createSegmentPath - 创建分段路径，点数: 50
"[绘制性能] new PenPathItem(segment) 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-400.075,-175.45 835.175x350.525) 项地址: 0x23a27e2c890
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
PenTool::createSegmentPath - 分段路径创建完成
"[绘制性能] PenTool::createSegmentPath 耗时: 0ms"
"[绘制性能] createSegmentPath 耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup 耗时: 0ms"
PenTool::manageSegments - 分段已添加到组，输入ID: -1 组中项目数: 3 分段项地址: 0x23a27e2c890 组地址: 0x23a22d40550
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-400.075,-175.45 835.175x350.525) 新边界: QRectF(-118,94 318x42.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] resetPath 耗时: 0ms"
PenTool::manageSegments - 重置当前路径，保留重叠点数: 3
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::manageSegments 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-118,94 318x42.275) 新边界: QRectF(-145,75 345x61.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 50 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 13ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 96 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-145,75 345x61.275) 新边界: QRectF(-196,8 396x128.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-196,8 396x128.275) 新边界: QRectF(-204,-14 404x150.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-204,-14 404x150.275) 新边界: QRectF(-232.5,-273 432.5x409.275) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 60 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-273 432.5x409.275) 新边界: QRectF(-232.5,-292.425 468.5x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 468.5x428.7) 新边界: QRectF(-232.5,-292.425 517.5x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 22 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 517.5x428.7) 新边界: QRectF(-232.5,-292.425 612.5x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 70 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 612.5x428.7) 新边界: QRectF(-232.5,-292.425 639.5x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 27 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 639.5x428.7) 新边界: QRectF(-232.5,-292.425 654.5x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
PenTool::onInputMove - 已处理 400 个移动事件 当前路径点数: 12 场景中图形项总数: 6
[绘制性能] ========== 性能统计摘要 ==========
"[绘制性能] pathItem->points():"
"  调用次数: 8"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputMove:"
"  调用次数: 537"
"  总耗时: 194ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 7ms"
"[绘制性能] PenTool::cleanupSegmentGroup:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::manageSegments:"
"  调用次数: 16"
"  总耗时: 18ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] rebuildPath(setPoints):"
"  调用次数: 17"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] new PenPathItem(segment):"
"  调用次数: 8"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::getOrCreateSegmentGroup:"
"  调用次数: 8"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::addPoint(first):"
"  调用次数: 2"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenTool::addPoint(final):"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::addPoint:"
"  调用次数: 400"
"  总耗时: 128ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 3ms"
"[绘制性能] buildPath:"
"  调用次数: 418"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createFinalPath:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 1ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::paint:"
"  调用次数: 1018"
"  总耗时: 1737ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 13ms"
"[绘制性能] PenTool::onInputRelease:"
"  调用次数: 1"
"  总耗时: 2ms"
"  平均耗时: 2ms"
"  最小耗时: 2ms"
"  最大耗时: 2ms"
"[绘制性能] calculateCurves:"
"  调用次数: 414"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] distanceCheck:"
"  调用次数: 403"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] appendPoint:"
"  调用次数: 401"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createFinalPathItem:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::setPoints:"
"  调用次数: 25"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] resetPath:"
"  调用次数: 8"
"  总耗时: 1ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] rebuildPath:"
"  调用次数: 401"
"  总耗时: 40ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] PenTool::finishPath:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] createSegmentPath:"
"  调用次数: 8"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createNewPath:"
"  调用次数: 4"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] cleanupSegmentGroup:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::createSegmentPath:"
"  调用次数: 8"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] addItemToScene:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath:"
"  调用次数: 418"
"  总耗时: 28ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] addFinalPathToScene:"
"  调用次数: 1"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] new PenPathItem:"
"  调用次数: 2"
"  总耗时: 0ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 0ms"
"[绘制性能] PenTool::onInputPress:"
"  调用次数: 2"
"  总耗时: 2ms"
"  平均耗时: 1ms"
"  最小耗时: 0ms"
"  最大耗时: 2ms"
"[绘制性能] setPath:"
"  调用次数: 418"
"  总耗时: 2ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 1ms"
"[绘制性能] PenPathItem::addPoint:"
"  调用次数: 403"
"  总耗时: 108ms"
"  平均耗时: 0ms"
"  最小耗时: 0ms"
"  最大耗时: 3ms"
[绘制性能] =====================================
"[绘制性能] PenTool::onInputMove 耗时: 6ms"
PenPathItem::paint - 调用# 80 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 32 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 654.5x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 90 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 26 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 23 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 100 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 25 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 2ms"
"[绘制性能] PenTool::addPoint 耗时: 2ms"
"[绘制性能] PenTool::onInputMove 耗时: 2ms"
PenPathItem::paint - 调用# 110 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
⚠️ 检测到卡顿! 距离上次move事件: 40 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 11ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 5ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 31 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 120 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 9ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenPathItem::paint 耗时: 8ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 3ms"
⚠️ 检测到卡顿! 距离上次move事件: 24 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
PenPathItem::paint - 调用# 130 暴露区域: QRectF(-400.075,-255.15 526.15x378.45) 项边界: QRectF(-400.075,-255.15 526.15x378.45) 重绘面积: 199121 项地址: 0x23a20bc6a80
"[绘制性能] PenPathItem::paint 耗时: 10ms"
"[绘制性能] PenPathItem::paint 耗时: 2ms"
"[绘制性能] PenPathItem::paint 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
"[绘制性能] PenPathItem::paint 耗时: 4ms"
⚠️ 检测到卡顿! 距离上次move事件: 29 ms
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] appendPoint 耗时: 0ms"
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(-232.5,-292.425 656.45x428.7) 新边界: QRectF(-232.5,-292.425 656.45x428.7) 项地址: 0x23a20bc6840
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 1ms"
"[绘制性能] PenTool::addPoint 耗时: 1ms"
"[绘制性能] PenTool::onInputMove 耗时: 1ms"
"[绘制性能] distanceCheck 耗时: 0ms"
"[绘制性能] PenPathItem::addPoint 耗时: 0ms"
"[绘制性能] PenTool::addPoint(final) 耗时: 0ms"
PenTool::onInputRelease - 绘制完成，最终点数: 25
PenTool::onInputRelease - 总点数: 167
PenTool::createFinalPath - 创建最终路径，总点数: 167
"[绘制性能] calculateCurves 耗时: 0ms"
"[绘制性能] buildPath 耗时: 0ms"
PenPathItem::setPath - 边界变化: 旧边界: QRectF(0,0 0x0) 新边界: QRectF(-400.075,-292.425 835.175x467.5) 项地址: 0x23a2a7616a0
"[绘制性能] setPath 耗时: 0ms"
"[绘制性能] PenPathItem::rebuildPath 耗时: 0ms"
"[绘制性能] rebuildPath(setPoints) 耗时: 0ms"
"[绘制性能] PenPathItem::setPoints 耗时: 0ms"
"[绘制性能] createFinalPathItem 耗时: 0ms"
"[绘制性能] addFinalPathToScene 耗时: 0ms"
PenTool::cleanupSegmentGroup - 清理分段组，输入ID: -1 组中项目数: 3
PenTool::cleanupSegmentGroup - 分段组已清理
"[绘制性能] PenTool::cleanupSegmentGroup 耗时: 0ms"
"[绘制性能] cleanupSegmentGroup 耗时: 0ms"
PenTool::createFinalPath - 最终路径创建完成
"[绘制性能] PenTool::createFinalPath 耗时: 0ms"
"[绘制性能] PenTool::createFinalPath 耗时: 0ms"
PenTool::finishPath - 完成路径绘制，最终点数: 167
PenTool::finishPath - 路径完成信号已发送
"[绘制性能] PenTool::finishPath 耗时: 0ms"
PenTool::onInputRelease - 所有绘制完成，退出绘制状态
"[绘制性能] PenTool::onInputRelease 耗时: 1ms"
"[绘制性能] PenPathItem::paint 耗时: 12ms"
"[绘制性能] PenPathItem::paint 耗时: 26ms"
⚠️ 检测到卡顿! 距离上次move事件: 45 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
⚠️ 检测到卡顿! 距离上次move事件: 17 ms
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
"[绘制性能] PenTool::onInputMove 耗时: 0ms"
