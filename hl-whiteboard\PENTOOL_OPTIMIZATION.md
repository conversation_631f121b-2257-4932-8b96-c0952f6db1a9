# PenTool性能优化 - QGraphicsItemGroup方案

## 问题描述
原始的PenTool实现中，分段机制创建的多个PenPathItem在场景中重叠存在，导致每次当前路径更新时都触发多个路径项的重复重绘，随着分段数量增加，重绘开销呈线性增长。

## 解决方案
使用QGraphicsItemGroup来管理分段路径项，将所有分段统一放入一个组中，绘制完毕后删除分段组，创建最终的完整路径项。

## 核心修改

### 1. 数据结构调整
```cpp
// 原来
QMap<int, QVector<PenPathItem*>> m_pathSegments;

// 现在
QMap<int, QGraphicsItemGroup*> m_segmentGroups;  // 分段组管理
QMap<int, QVector<QPointF>> m_allPoints;         // 完整点数据存储
```

### 2. 新增方法
- `getOrCreateSegmentGroup(int inputId)`: 获取或创建分段组
- `cleanupSegmentGroup(int inputId)`: 清理分段组
- `createFinalPath(int inputId)`: 创建最终完整路径

### 3. 绘制流程优化
1. **开始绘制**: 创建当前路径项，初始化点数据存储
2. **分段管理**: 达到50个点时，创建分段项并添加到组中，重置当前路径
3. **实时绘制**: 只有当前路径项参与实时重绘，分段组不参与
4. **完成绘制**: 删除分段组和当前路径项，用所有点创建最终完整路径

## 性能优化效果

### 1. 减少重绘开销
- **原来**: 每次move事件重绘所有重叠的分段路径项
- **现在**: 只重绘当前活跃的路径项

### 2. 场景图形项管理
- **原来**: 场景中存在多个独立的PenPathItem
- **现在**: 场景中只有分段组和当前路径项

### 3. 内存管理优化
- **组的自动清理**: 删除组时自动清理所有子项
- **最终路径**: 只保留一个完整的路径项

## 关键代码变更

### manageSegments方法
```cpp
// 创建分段项后添加到组中，而不是直接添加到场景
QGraphicsItemGroup* segmentGroup = getOrCreateSegmentGroup(inputId);
segmentGroup->addToGroup(segmentItem);
```

### onInputRelease方法
```cpp
// 绘制完成时创建最终完整路径
PenPathItem* finalPath = createFinalPath(inputId);
if (finalPath) {
    finishPath(finalPath);
}
```

### createFinalPath方法
```cpp
// 使用所有点数据创建完整路径
PenPathItem* finalPath = new PenPathItem();
finalPath->setPoints(m_allPoints[inputId]);

// 清理分段组和当前路径项
cleanupSegmentGroup(inputId);
removeItemFromScene(currentPath);
```

## 预期效果
1. **性能提升**: 解决多路径项重叠重绘问题，绘制性能不再随路径长度下降
2. **内存优化**: 减少场景中的图形项数量，优化内存使用
3. **用户体验**: 保持绘制过程的流畅性，最终结果完整性不变
4. **代码维护**: 简化分段管理逻辑，提高代码可维护性

## 测试验证
可以通过以下方式验证优化效果：
1. 绘制长路径，观察性能分析器输出
2. 检查场景中图形项数量变化
3. 对比优化前后的重绘面积和频率
4. 验证最终路径的完整性和正确性
