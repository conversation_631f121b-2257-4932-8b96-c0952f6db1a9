#include "pentool.h"
#include "../items/penpathitem.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QDebug>
#include <QGraphicsScene>
#include <QGraphicsItemGroup>

PenTool::PenTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Pen, parent)
{
    // 启用性能分析器
    DRAWING_PROFILER_ENABLE(true);
    qDebug() << "PenTool::PenTool - 性能分析器已启用";
}

PenTool::~PenTool()
{
    finishCurrentOperation();
}

void PenTool::onInputPress(int inputId, const QPointF &scenePos)
{
    DRAWING_TIMER("PenTool::onInputPress");

    qDebug() << "PenTool::onInputPress - 开始绘制，输入ID:" << inputId << "位置:" << scenePos;

    m_isDrawing = true;

    // 创建新的路径项
    int createPathTimer = DRAWING_TIMER_START("PenTool::createNewPath");
    PenPathItem *pathItem = createNewPath(scenePos);
    DRAWING_TIMER_END(createPathTimer);

    // 保存到活跃路径映射
    m_activePaths[inputId] = pathItem;

    // 初始化点数据存储
    m_allPoints[inputId] = QVector<QPointF>();
    m_allPoints[inputId].append(scenePos);

    qDebug() << "PenTool::onInputPress - 路径创建完成，活跃路径数:" << m_activePaths.size();
}

void PenTool::onInputMove(int inputId, const QPointF &scenePos)
{
    DRAWING_TIMER("PenTool::onInputMove");

    // 卡顿检测
    static QElapsedTimer lagDetector;
    static bool lagDetectorStarted = false;
    if (!lagDetectorStarted) {
        lagDetector.start();
        lagDetectorStarted = true;
    }

    qint64 timeSinceLastMove = lagDetector.restart();
    if (timeSinceLastMove > 16) { // 超过16ms表示可能卡顿（60fps标准）
        qDebug() << "⚠️ 检测到卡顿! 距离上次move事件:" << timeSinceLastMove << "ms";
    }

    if (!m_activePaths.contains(inputId)) {
        return;
    }

    PenPathItem *pathItem = m_activePaths[inputId];
    if (pathItem) {
        // 添加点到路径
        int addPointTimer = DRAWING_TIMER_START("PenTool::addPoint");
        pathItem->addPoint(scenePos);
        DRAWING_TIMER_END(addPointTimer);

        // 同时保存到完整点数据中
        if (m_allPoints.contains(inputId)) {
            m_allPoints[inputId].append(scenePos);
        }

        // 检查是否需要管理分段（每100个点创建一个分段，减少分段频率）
        if (pathItem->pointCount() % 100 == 0 && pathItem->pointCount() > 0) {
            int segmentTimer = DRAWING_TIMER_START("PenTool::manageSegments");
            manageSegments(inputId, pathItem);
            DRAWING_TIMER_END(segmentTimer);
        }

        // 每100个点打印一次统计信息
        static int moveCount = 0;
        moveCount++;
        if (moveCount % 100 == 0) {
            // 统计场景中的图形项数量
            int sceneItemCount = 0;
            if (scene()) {
                sceneItemCount = scene()->items().size();
            }

            qDebug() << "PenTool::onInputMove - 已处理" << moveCount << "个移动事件"
                     << "当前路径点数:" << pathItem->pointCount()
                     << "场景中图形项总数:" << sceneItemCount;

            // 打印性能统计摘要
            DRAWING_PROFILER_SUMMARY();
        }
    }
}

void PenTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    DRAWING_TIMER("PenTool::onInputRelease");

    if (!m_activePaths.contains(inputId)) {
        return;
    }

    PenPathItem *pathItem = m_activePaths[inputId];
    if (pathItem) {
        // 添加最后一个点
        int addPointTimer = DRAWING_TIMER_START("PenTool::addPoint(final)");
        pathItem->addPoint(scenePos);
        DRAWING_TIMER_END(addPointTimer);

        // 同时保存到完整点数据中
        if (m_allPoints.contains(inputId)) {
            m_allPoints[inputId].append(scenePos);
        }

        qDebug() << "PenTool::onInputRelease - 绘制完成，最终点数:" << pathItem->pointCount();
        qDebug() << "PenTool::onInputRelease - 总点数:" << m_allPoints[inputId].size();

        // 创建最终完整路径并清理分段
        int finalPathTimer = DRAWING_TIMER_START("PenTool::createFinalPath");
        PenPathItem* finalPath = createFinalPath(inputId);
        DRAWING_TIMER_END(finalPathTimer);

        if (finalPath) {
            // 完成路径
            int finishTimer = DRAWING_TIMER_START("PenTool::finishPath");
            finishPath(finalPath);
            DRAWING_TIMER_END(finishTimer);
        }
    }

    // 清理状态
    m_activePaths.remove(inputId);
    m_allPoints.remove(inputId);

    // 如果没有活跃路径了，设置为非绘制状态
    if (m_activePaths.isEmpty()) {
        m_isDrawing = false;
        qDebug() << "PenTool::onInputRelease - 所有绘制完成，退出绘制状态";
    }
}

void PenTool::finishCurrentOperation()
{
    // 完成所有活跃的绘制操作
    QList<int> inputIds = m_activePaths.keys();
    for (int inputId : inputIds) {
        PenPathItem *pathItem = m_activePaths[inputId];
        if (pathItem && m_allPoints.contains(inputId)) {
            // 创建最终完整路径
            PenPathItem* finalPath = createFinalPath(inputId);
            if (finalPath) {
                finishPath(finalPath);
            }
        }
    }

    // 清理状态
    m_activePaths.clear();
    m_allPoints.clear();
    m_segmentGroups.clear();
    m_isDrawing = false;

    BaseTool::finishCurrentOperation();
}

void PenTool::cancelCurrentOperation()
{
    // 取消所有活跃的绘制操作
    for (auto it = m_activePaths.begin(); it != m_activePaths.end(); ++it) {
        PenPathItem *pathItem = it.value();
        if (pathItem) {
            // 从场景中移除
            removeItemFromScene(pathItem);
            delete pathItem;
        }
    }

    // 清理所有分段组
    QList<int> inputIds = m_segmentGroups.keys();
    for (int inputId : inputIds) {
        cleanupSegmentGroup(inputId);
    }

    // 清理状态
    m_activePaths.clear();
    m_allPoints.clear();
    m_segmentGroups.clear();
    m_isDrawing = false;

    BaseTool::cancelCurrentOperation();
}

PenPathItem* PenTool::createNewPath(const QPointF &startPoint)
{
    DRAWING_TIMER("PenTool::createNewPath");

    qDebug() << "PenTool::createNewPath - 创建新路径，起始点:" << startPoint;

    int createItemTimer = DRAWING_TIMER_START("new PenPathItem");
    PenPathItem *pathItem = new PenPathItem();
    DRAWING_TIMER_END(createItemTimer);

    // 注意：分段绘制现在由PenTool管理

    int addPointTimer = DRAWING_TIMER_START("PenPathItem::addPoint(first)");
    pathItem->addPoint(startPoint);
    DRAWING_TIMER_END(addPointTimer);

    int addToSceneTimer = DRAWING_TIMER_START("addItemToScene");
    addItemToScene(pathItem);
    DRAWING_TIMER_END(addToSceneTimer);

    qDebug() << "PenTool::createNewPath - 路径创建并添加到场景完成";
    return pathItem;
}

void PenTool::finishPath(PenPathItem *pathItem)
{
    if (!pathItem) {
        return;
    }

    qDebug() << "PenTool::finishPath - 完成路径绘制，最终点数:" << pathItem->pointCount();

    emit itemFinished(pathItem);
    qDebug() << "PenTool::finishPath - 路径完成信号已发送";
}

PenPathItem* PenTool::createSegmentPath(const QVector<QPointF> &points)
{
    DRAWING_TIMER("PenTool::createSegmentPath");

    if (points.size() < 2) {
        return nullptr;
    }

    qDebug() << "PenTool::createSegmentPath - 创建分段路径，点数:" << points.size();

    // 创建分段路径项
    int createItemTimer = DRAWING_TIMER_START("new PenPathItem(segment)");
    PenPathItem* segmentItem = new PenPathItem();
    DRAWING_TIMER_END(createItemTimer);

    // 设置点并构建路径
    int setPointsTimer = DRAWING_TIMER_START("PenPathItem::setPoints");
    segmentItem->setPoints(points);
    DRAWING_TIMER_END(setPointsTimer);

    // 注意：不直接添加到场景，而是由调用者添加到分段组中

    qDebug() << "PenTool::createSegmentPath - 分段路径创建完成";
    return segmentItem;
}

QGraphicsItemGroup* PenTool::getOrCreateSegmentGroup(int inputId)
{
    DRAWING_TIMER("PenTool::getOrCreateSegmentGroup");

    if (!m_segmentGroups.contains(inputId)) {
        // 创建新的分段组
        QGraphicsItemGroup* group = new QGraphicsItemGroup();

        // 添加到场景
        addItemToScene(group);

        // 保存到映射
        m_segmentGroups[inputId] = group;

        qDebug() << "PenTool::getOrCreateSegmentGroup - 创建新分段组，输入ID:" << inputId;
    }

    return m_segmentGroups[inputId];
}

void PenTool::cleanupSegmentGroup(int inputId)
{
    DRAWING_TIMER("PenTool::cleanupSegmentGroup");

    if (m_segmentGroups.contains(inputId)) {
        QGraphicsItemGroup* group = m_segmentGroups[inputId];

        qDebug() << "PenTool::cleanupSegmentGroup - 清理分段组，输入ID:" << inputId
                 << "组中项目数:" << group->childItems().size();

        // 从场景中移除组（会自动清理所有子项）
        removeItemFromScene(group);

        // 删除组对象
        delete group;

        // 从映射中移除
        m_segmentGroups.remove(inputId);

        qDebug() << "PenTool::cleanupSegmentGroup - 分段组已清理";
    }
}

PenPathItem* PenTool::createFinalPath(int inputId)
{
    DRAWING_TIMER("PenTool::createFinalPath");

    if (!m_allPoints.contains(inputId) || m_allPoints[inputId].isEmpty()) {
        qDebug() << "PenTool::createFinalPath - 没有找到点数据，输入ID:" << inputId;
        return nullptr;
    }

    const QVector<QPointF>& allPoints = m_allPoints[inputId];
    qDebug() << "PenTool::createFinalPath - 创建最终路径，总点数:" << allPoints.size();

    // 创建最终的完整路径项
    int createFinalTimer = DRAWING_TIMER_START("createFinalPathItem");
    PenPathItem* finalPath = new PenPathItem();
    finalPath->setPoints(allPoints);
    DRAWING_TIMER_END(createFinalTimer);

    // 添加到场景
    int addToSceneTimer = DRAWING_TIMER_START("addFinalPathToScene");
    addItemToScene(finalPath);
    DRAWING_TIMER_END(addToSceneTimer);

    // 清理分段组
    int cleanupTimer = DRAWING_TIMER_START("cleanupSegmentGroup");
    cleanupSegmentGroup(inputId);
    DRAWING_TIMER_END(cleanupTimer);

    // 移除当前活跃路径项
    if (m_activePaths.contains(inputId)) {
        PenPathItem* currentPath = m_activePaths[inputId];
        removeItemFromScene(currentPath);
        delete currentPath;
    }

    qDebug() << "PenTool::createFinalPath - 最终路径创建完成";
    return finalPath;
}

void PenTool::manageSegments(int inputId, PenPathItem *pathItem)
{
    DRAWING_TIMER("PenTool::manageSegments");

    if (!pathItem) {
        return;
    }

    qDebug() << "PenTool::manageSegments - 创建分段，当前点数:" << pathItem->pointCount();

    // 获取所有点作为分段数据
    int getPointsTimer = DRAWING_TIMER_START("pathItem->points()");
    QVector<QPointF> allPoints = pathItem->points();
    DRAWING_TIMER_END(getPointsTimer);

    if (allPoints.size() >= 100) {
        // 创建分段路径项（只包含前面的点，不包含重叠部分）
        int segmentSize = allPoints.size() - 10; // 保留10个点用于重叠
        QVector<QPointF> segmentPoints = allPoints.mid(0, segmentSize);

        int createSegmentTimer = DRAWING_TIMER_START("createSegmentPath");
        PenPathItem* segmentItem = createSegmentPath(segmentPoints);
        DRAWING_TIMER_END(createSegmentTimer);

        if (segmentItem) {
            // 获取或创建分段组
            QGraphicsItemGroup* segmentGroup = getOrCreateSegmentGroup(inputId);

            // 将分段项添加到组中，而不是直接添加到场景
            segmentGroup->addToGroup(segmentItem);

            qDebug() << "PenTool::manageSegments - 分段已添加到组，输入ID:" << inputId
                     << "组中项目数:" << segmentGroup->childItems().size()
                     << "分段项地址:" << (void*)segmentItem
                     << "组地址:" << (void*)segmentGroup;

            // 清空当前路径，保留更多点用于平滑连接
            int resetPathTimer = DRAWING_TIMER_START("resetPath");
            QVector<QPointF> overlap;
            int overlapSize = qMin(10, allPoints.size()); // 增加重叠点数
            for (int i = allPoints.size() - overlapSize; i < allPoints.size(); ++i) {
                overlap.append(allPoints[i]);
            }

            pathItem->setPoints(overlap);
            DRAWING_TIMER_END(resetPathTimer);

            qDebug() << "PenTool::manageSegments - 重置当前路径，保留重叠点数:" << overlap.size();
        }
    }
}


