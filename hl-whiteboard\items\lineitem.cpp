#include "lineitem.h"
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QtMath>
#include <QDebug>

LineItem::LineItem(QGraphicsItem *parent)
    : QGraphicsLineItem(parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Line, this)
{
}

LineItem::LineItem(const QLineF &line, QGraphicsItem *parent)
    : QGraphicsLineItem(line, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Line, this)
{
}

LineItem::LineItem(qreal x1, qreal y1, qreal x2, qreal y2, QGraphicsItem *parent)
    : QGraphicsLineItem(x1, y1, x2, y2, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Line, this)
{
}

LineItem::~LineItem()
{
}

QJsonObject LineItem::serializeGeometry() const
{
    QJsonObject json;

    // 直线几何信息
    QLineF currentLine = line();
    QJsonObject lineObj;
    lineObj["x1"] = currentLine.x1();
    lineObj["y1"] = currentLine.y1();
    lineObj["x2"] = currentLine.x2();
    lineObj["y2"] = currentLine.y2();
    json["line"] = lineObj;

    return json;
}

bool LineItem::deserializeGeometry(const QJsonObject &json)
{
    try {
        // 读取直线几何信息
        QJsonObject lineObj = json["line"].toObject();
        QLineF newLine(
            lineObj["x1"].toDouble(),
            lineObj["y1"].toDouble(),
            lineObj["x2"].toDouble(),
            lineObj["y2"].toDouble()
        );
        setLine(newLine);

        return true;
    } catch (...) {
        qWarning() << "LineItem::deserializeGeometry: Exception occurred";
        return false;
    }
}


void LineItem::setLine(const QLineF &line)
{
    QGraphicsLineItem::setLine(line);
}

void LineItem::setLine(qreal x1, qreal y1, qreal x2, qreal y2)
{
    setLine(QLineF(x1, y1, x2, y2));
}

void LineItem::setLine(const QPointF &p1, const QPointF &p2)
{
    setLine(QLineF(p1, p2));
}

QLineF LineItem::lineF() const
{
    return line();
}

void LineItem::setStartPoint(const QPointF &point)
{
    QLineF currentLine = line();
    setLine(QLineF(point, currentLine.p2()));
}

QPointF LineItem::startPoint() const
{
    return line().p1();
}

void LineItem::setEndPoint(const QPointF &point)
{
    QLineF currentLine = line();
    setLine(QLineF(currentLine.p1(), point));
}

QPointF LineItem::endPoint() const
{
    return line().p2();
}

qreal LineItem::length() const
{
    return line().length();
}

qreal LineItem::angle() const
{
    return qDegreesToRadians(line().angle());
}

qreal LineItem::angleDegrees() const
{
    return line().angle();
}

QPointF LineItem::center() const
{
    return line().center();
}

void LineItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // 设置抗锯齿
    setupAntiAliasing(painter);

    // 使用默认的直线绘制
    QGraphicsLineItem::paint(painter, option, widget);
}
