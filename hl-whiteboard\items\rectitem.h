#ifndef RectItem_H
#define RectItem_H

#include <QGraphicsRectItem>
#include <QJsonObject>
#include "base/drawingitembase.h"

/**
 * @brief RectItem - 矩形绘图项
 *
 * 继承自 QGraphicsRectItem 和 DrawingItemBase，实现矩形绘制功能。
 * 支持抗锯齿绘制和 JSON 序列化。
 * 通用功能（样式、变换等）已在基类中实现。
 */
class RectItem : public QGraphicsRectItem, public DrawingItemBase
{
public:
    explicit RectItem(QGraphicsItem *parent = nullptr);
    explicit RectItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    explicit RectItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem *parent = nullptr);
    ~RectItem();

    // 实现 DrawingItemBase 的几何序列化接口
    QJsonObject serializeGeometry() const override;
    bool deserializeGeometry(const QJsonObject &json) override;

    // 矩形操作
    void setRectangle(const QRectF &rect);
    void setRectangle(qreal x, qreal y, qreal width, qreal height);
    QRectF rectangle() const;

    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
};

#endif // RectItem_H
