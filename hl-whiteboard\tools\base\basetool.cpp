#include "basetool.h"
#include "../toolmanager.h"
#include "../../items/base/drawingitembase.h"
#include <QGraphicsScene>
#include <QDebug>


BaseTool::BaseTool(WhiteboardTypes::DrawingType type, QObject *parent)
    : QObject(parent)
    , m_toolType(type)
    , m_isDrawing(false)
    , m_scene(nullptr)
{
}

BaseTool::~BaseTool()
{
}

WhiteboardTypes::DrawingType BaseTool::toolType() const
{
    return m_toolType;
}

void BaseTool::setScene(QGraphicsScene *scene)
{
    m_scene = scene;
}

QGraphicsScene* BaseTool::scene() const
{
    return m_scene;
}



void BaseTool::finishCurrentOperation()
{
    // 默认实现：子类可以重写
}

void BaseTool::cancelCurrentOperation()
{
    // 默认实现：结束当前操作
    finishCurrentOperation();
}

bool BaseTool::isDrawing() const
{
    return m_isDrawing;
}

void BaseTool::addItemToScene(QGraphicsItem *item)
{
    QGraphicsScene *currentScene = scene();
    if (item && currentScene) {
        // 应用当前样式到图形项
        // 对于标准 QGraphicsItem，直接设置 pen 和 brush
        if (auto pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(item)) {
            pathItem->setPen(getCurrentPen());
            pathItem->setBrush(getCurrentBrush());
        } else if (auto rectItem = qgraphicsitem_cast<QGraphicsRectItem*>(item)) {
            rectItem->setPen(getCurrentPen());
            rectItem->setBrush(getCurrentBrush());
        } else if (auto ellipseItem = qgraphicsitem_cast<QGraphicsEllipseItem*>(item)) {
            ellipseItem->setPen(getCurrentPen());
            ellipseItem->setBrush(getCurrentBrush());
        } else if (auto lineItem = qgraphicsitem_cast<QGraphicsLineItem*>(item)) {
            lineItem->setPen(getCurrentPen());
        }

        // 如果是 DrawingItemBase，设置抗锯齿
        if (auto drawingItem = dynamic_cast<DrawingItemBase*>(item)) {
            drawingItem->setAntiAliasing(getCurrentAntiAliasing());
        }

        // 添加到场景
        currentScene->addItem(item);

        emit itemCreated(item);
    }
}

void BaseTool::removeItemFromScene(QGraphicsItem *item)
{
    QGraphicsScene *currentScene = scene();
    if (item && currentScene) {
        currentScene->removeItem(item);
    }
}

// 从ToolManager获取当前样式设置
QPen BaseTool::getCurrentPen() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    if (toolManager) {
        QPen pen(toolManager->penColor());
        pen.setWidthF(toolManager->penWidth());
        pen.setCapStyle(Qt::RoundCap);
        pen.setJoinStyle(Qt::RoundJoin);
        return pen;
    }
    return QPen(Qt::black, 2.0);
}

QBrush BaseTool::getCurrentBrush() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    if (toolManager) {
        QColor brushColor = toolManager->brushColor();
        if (brushColor.alpha() == 0) {
            return QBrush(Qt::NoBrush);
        }
        return QBrush(brushColor);
    }
    return QBrush(Qt::NoBrush);
}

QColor BaseTool::getCurrentPenColor() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    return toolManager ? toolManager->penColor() : Qt::black;
}

qreal BaseTool::getCurrentPenWidth() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    return toolManager ? toolManager->penWidth() : 2.0;
}

QColor BaseTool::getCurrentBrushColor() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    return toolManager ? toolManager->brushColor() : Qt::transparent;
}

bool BaseTool::getCurrentAntiAliasing() const
{
    ToolManager *toolManager = qobject_cast<ToolManager*>(parent());
    return toolManager ? toolManager->antiAliasing() : true;
}


