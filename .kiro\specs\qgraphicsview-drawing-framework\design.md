# Design Document

## Overview

本设计文档描述了基于 QGraphicsView 的绘图框架架构，该框架将为 hl-whiteboard 项目提供完整的绘图解决方案。设计遵循 Qt 的图形视图框架标准，采用 Model-View 架构模式，确保高性能、高可维护性和良好的扩展性。

核心设计原则：
- 基于 QGraphicsView、QGraphicsScene、QGraphicsItem 的标准三层架构
- 单一职责原则，每个类专注于特定功能
- 高内聚低耦合，最小化模块间依赖
- 通过 whiteboard.h 统一暴露简洁的 API 接口
- 使用信号槽机制实现松耦合的组件通信

## Architecture

### 整体架构图

```mermaid
graph TB
    subgraph "External Interface"
        WB[Whiteboard - 统一接口]
    end
    
    subgraph "View Layer"
        WBV[WhiteboardView - QGraphicsView]
    end
    
    subgraph "Scene Layer"
        WBS[WhiteboardScene - QGraphicsScene]
        SM[SceneManager - 场景管理]
    end
    
    subgraph "Item Layer"
        DI[DrawingItem - 基础绘图项包装器]
        QPI[QGraphicsPathItem - 自由绘图路径]
        QRI[QGraphicsRectItem - 矩形]
        QEI[QGraphicsEllipseItem - 椭圆/圆形]
        QLI[QGraphicsLineItem - 直线]
        QPO[QGraphicsPolygonItem - 多边形]
        GI[QGraphicsItemGroup - 组合项]
    end
    
    subgraph "Tool System"
        TM[ToolManager - 工具管理]
        PT[PenTool - 画笔工具]
        ET[EraserTool - 橡皮擦工具]
        RT[RectangleTool - 矩形工具]
        ELT[EllipseTool - 椭圆工具]
        LIT[LineTool - 直线工具]
        LT[LassoTool - 套索工具]
    end
    
    subgraph "Command System"
        CM[CommandManager - 命令管理]
        UC[UndoCommand - 撤销命令基类]
        DC[DrawCommand - 绘制命令]
        EC[EraseCommand - 擦除命令]
    end
    
    WB --> WBV
    WB --> TM
    WB --> CM
    WBV --> WBS
    WBS --> SM
    WBS --> DI
    DI --> QPI
    DI --> QRI
    DI --> QEI
    DI --> QLI
    DI --> QPO
    DI --> GI
    TM --> PT
    TM --> ET
    TM --> RT
    TM --> ELT
    TM --> LIT
    TM --> LT
    CM --> UC
    UC --> DC
    UC --> EC
```

### 核心架构层次

1. **External Interface Layer (外部接口层)**
   - `Whiteboard`: 统一的外部接口，封装所有功能

2. **View Layer (视图层)**
   - `WhiteboardView`: 继承自 QGraphicsView，处理用户交互和触屏事件

3. **Scene Layer (场景层)**
   - `WhiteboardScene`: 继承自 QGraphicsScene，管理图形项
   - `SceneManager`: 管理场景状态和图形项的生命周期

4. **Item Layer (图形项层)**
   - `DrawingItem`: 所有绘图项的基类
   - `PathItem`: 自由绘制路径项
   - `ShapeItem`: 几何形状项
   - `GroupItem`: 组合图形项

5. **Tool System (工具系统)**
   - `ToolManager`: 工具管理器，控制当前活动工具
   - 各种具体工具类：`PenTool`, `EraserTool`, `ShapeTool`, `LassoTool`

6. **Command System (命令系统)**
   - `CommandManager`: 命令管理器，实现撤销/重做
   - 各种命令类：继承自 `UndoCommand` 的具体命令

## 事件处理层次说明

### 为什么需要三层事件处理？

Qt 图形视图框架的事件处理遵循特定的层次结构，每一层都有其特定的职责：

#### 1. WhiteboardView 层的鼠标处理职责
```cpp
// WhiteboardView 只处理以下类型的鼠标事件：
void WhiteboardView::mousePressEvent(QMouseEvent *event)
{
    // 1. 检测是否为触屏冲突
    if (m_touchActive) {
        event->ignore();
        return;
    }
    
    // 2. 处理视图级别的操作（如平移视图）
    if (event->button() == Qt::MiddleButton) {
        m_isPanning = true;
        m_lastPanPoint = event->pos();
        event->accept();
        return;
    }
    
    // 3. 其他绘图相关事件传递给父类，最终到达 Scene
    QGraphicsView::mousePressEvent(event);
}
```

**WhiteboardView 处理鼠标事件的原因：**
- 触屏和鼠标冲突检测（必须在最外层处理）
- 视图平移操作（属于视图变换，不是绘图操作）
- 事件过滤和预处理

#### 2. WhiteboardScene 层的鼠标处理职责
```cpp
// WhiteboardScene 处理场景坐标系下的事件
void WhiteboardScene::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    // 1. 坐标转换：从视图坐标转换为场景坐标
    QPointF scenePos = event->scenePos();
    
    // 2. 检查是否点击在现有图形项上
    QGraphicsItem *item = itemAt(scenePos, QTransform());
    if (item && event->button() == Qt::LeftButton) {
        // 处理图形项选择逻辑
        handleItemSelection(item);
    }
    
    // 3. 将绘图事件转发给 ToolManager
    if (m_toolManager) {
        m_toolManager->handleMousePress(event);
    }
    
    // 4. 调用父类处理其他默认行为
    QGraphicsScene::mousePressEvent(event);
}
```

**WhiteboardScene 处理鼠标事件的原因：**
- 坐标系转换（从视图坐标转为场景坐标）
- 图形项选择和交互管理
- 场景级别的事件分发
- 无限扩展场景的边界管理

#### 3. ToolManager 层的鼠标处理职责
```cpp
// ToolManager 处理具体的绘图逻辑
void ToolManager::handleMousePress(QGraphicsSceneMouseEvent *event)
{
    // 根据当前工具执行具体的绘图操作
    BaseTool *currentTool = getCurrentToolInstance();
    if (currentTool) {
        currentTool->onMousePress(event);
    }
}
```

**ToolManager 处理鼠标事件的原因：**
- 根据当前工具类型执行不同的绘图逻辑
- 创建和管理绘图项
- 处理工具特定的交互行为

### 事件流向图

```mermaid
sequenceDiagram
    participant User
    participant View as WhiteboardView
    participant Scene as WhiteboardScene
    participant Tool as ToolManager
    participant Item as DrawingItem

    User->>View: 鼠标点击
    
    alt 触屏冲突检测
        View->>View: 检查 m_touchActive
        View-->>User: 忽略事件
    else 视图操作（中键平移）
        View->>View: 处理视图平移
        View-->>User: 事件已处理
    else 绘图操作
        View->>Scene: 传递事件 (QGraphicsView::mousePressEvent)
        Scene->>Scene: 坐标转换 (视图→场景)
        Scene->>Scene: 检查图形项选择
        Scene->>Tool: 转发绘图事件
        Tool->>Tool: 根据当前工具处理
        Tool->>Item: 创建或修改图形项
        Item-->>Scene: 添加到场景
        Scene-->>View: 更新视图
        View-->>User: 显示结果
    end
```

### 为什么不能合并这些层？

1. **职责分离原则**：每层处理不同类型的事件和逻辑
2. **坐标系统**：View 使用视图坐标，Scene 使用场景坐标
3. **Qt 框架要求**：QGraphicsView 框架的标准事件处理流程
4. **扩展性**：便于添加新的视图操作或绘图工具
5. **维护性**：清晰的层次结构便于调试和维护

## 统一类型定义

```cpp
// 统一的类型枚举，避免重复定义
namespace WhiteboardTypes {
    enum class DrawingType {
        Pen,           // 画笔工具
        Eraser,        // 橡皮擦工具
        Rectangle,     // 矩形工具
        Ellipse,       // 椭圆工具
        Line,          // 直线工具
        Lasso,         // 套索选择工具
        Group          // 组合项
    };
    
    // 类型转换辅助函数
    QString drawingTypeToString(DrawingType type);
    DrawingType stringToDrawingType(const QString &str);
}
```

## Components and Interfaces

### 1. Whiteboard (统一接口类)

```cpp
class Whiteboard : public QWidget
{
    Q_OBJECT

public:
    explicit Whiteboard(QWidget *parent = nullptr);
    ~Whiteboard();

    // 工具管理
    void setCurrentTool(WhiteboardTypes::DrawingType tool);
    WhiteboardTypes::DrawingType currentTool() const;
    
    // 撤销/重做
    void undo();
    void redo();
    bool canUndo() const;
    bool canRedo() const;
    
    
    // 场景管理
    void clearScene();
    void saveScene(const QString &filename);
    void loadScene(const QString &filename);
    
    // JSON 导入导出
    QJsonObject exportToJson() const;
    bool importFromJson(const QJsonObject &json);

signals:
    void toolChanged(WhiteboardTypes::DrawingType tool);
    void canUndoChanged(bool canUndo);
    void canRedoChanged(bool canRedo);
    void sceneChanged();

private:
    class WhiteboardPrivate;
    std::unique_ptr<WhiteboardPrivate> d;
};
```

### 2. WhiteboardView (视图类)

```cpp
class WhiteboardView : public QGraphicsView
{
    Q_OBJECT

public:
    explicit WhiteboardView(QWidget *parent = nullptr);
    ~WhiteboardView();

protected:
    // 触屏和鼠标事件处理
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    
    // 触屏多指操作支持
    bool event(QEvent *event) override;
    void touchEvent(QTouchEvent *event);

private:
    void handleTouchGestures(QTouchEvent *event);
    void handleMouseInput(QMouseEvent *event);
    bool isMouseInputValid(QMouseEvent *event) const;
    
    // 多指绘制支持
    void handleMultiTouchDrawing(QTouchEvent *event);
    void processTouchPoint(const QTouchEvent::TouchPoint &touchPoint);
    
    bool m_touchActive;  // 防止触屏和鼠标冲突
    
    // 多指绘制状态管理
    QMap<int, QPointF> m_activeTouchPoints;  // 活跃的触摸点
    QSet<int> m_drawingTouchIds;             // 正在绘制的触摸点ID
};
```

### 3. WhiteboardScene (场景类)

```cpp
class WhiteboardScene : public QGraphicsScene
{
    Q_OBJECT

public:
    explicit WhiteboardScene(QObject *parent = nullptr);
    ~WhiteboardScene();

    void setSceneManager(SceneManager *manager);
    SceneManager* sceneManager() const;
    
    void setToolManager(ToolManager *manager);
    ToolManager* toolManager() const;
    
    // 无限扩展场景支持
    void expandSceneRect(const QRectF &rect);
    void ensureVisible(const QPointF &point);

protected:
    // Scene层的事件处理：将绘图相关事件转发给ToolManager
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent *event) override;

signals:
    void itemAdded(DrawingItem *item);
    void itemRemoved(DrawingItem *item);
    void selectionChanged();

private:
    SceneManager *m_sceneManager;
    ToolManager *m_toolManager;
    QRectF m_infiniteRect;  // 支持无限扩展的场景矩形
};
```

### 4. 基于 Qt 标准 QGraphicsItem 的绘图项设计

我们将充分利用 Qt 提供的现成 QGraphicsItem 子类，而不是重新实现所有功能：

#### 4.1 DrawingItemBase (绘图项基类)

```cpp
// 所有绘图项的统一基类接口
class DrawingItemBase
{
public:
    explicit DrawingItemBase(WhiteboardTypes::DrawingType type);
    virtual ~DrawingItemBase();

    // 类型信息
    WhiteboardTypes::DrawingType drawingType() const;

    // JSON 序列化支持
    virtual QJsonObject toJson() const = 0;
    virtual bool fromJson(const QJsonObject &json) = 0;

    // 统一的样式接口
    virtual void setPenColor(const QColor &color) = 0;
    virtual QColor penColor() const = 0;
    
    virtual void setPenWidth(qreal width) = 0;
    virtual qreal penWidth() const = 0;
    
    virtual void setBrushColor(const QColor &color) = 0;
    virtual QColor brushColor() const = 0;

    // 抗锯齿支持
    void setAntiAliasing(bool enabled);
    bool antiAliasing() const;

    // 元数据管理
    void setMetadata(const QJsonObject &metadata);
    QJsonObject metadata() const;

protected:
    // 抗锯齿绘制辅助函数
    void setupAntiAliasing(QPainter *painter) const;

private:
    WhiteboardTypes::DrawingType m_type;
    bool m_antiAliasing;
    QJsonObject m_metadata;
};
```

#### 4.2 具体的 Qt 图形项使用（多重继承方案）

```cpp
// 1. 自由绘图路径 - 继承 QGraphicsPathItem 和 DrawingItemBase
class PenPathItem : public QGraphicsPathItem, public DrawingItemBase
{
public:
    explicit PenPathItem(QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    // 路径特有功能
    void addPoint(const QPointF &point);
    void smoothPath();
    
    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    QVector<QPointF> m_points;
};

// 2. 矩形 - 继承 QGraphicsRectItem 和 DrawingItemBase
class RectItem : public QGraphicsRectItem, public DrawingItemBase
{
public:
    explicit RectItem(QGraphicsItem *parent = nullptr);
    explicit RectItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    // 矩形特有功能
    void setCornerRadius(qreal radius);
    qreal cornerRadius() const;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    qreal m_cornerRadius;
};

// 3. 椭圆/圆形 - 继承 QGraphicsEllipseItem 和 DrawingItemBase
class EllipseItem : public QGraphicsEllipseItem, public DrawingItemBase
{
public:
    explicit EllipseItem(QGraphicsItem *parent = nullptr);
    explicit EllipseItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
};

// 4. 直线 - 继承 QGraphicsLineItem 和 DrawingItemBase
class LineItem : public QGraphicsLineItem, public DrawingItemBase
{
public:
    explicit LineItem(QGraphicsItem *parent = nullptr);
    explicit LineItem(const QLineF &line, QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    // 直线特有功能
    enum ArrowStyle { NoArrow, StartArrow, EndArrow, BothArrows };
    void setArrowStyle(ArrowStyle style);
    ArrowStyle arrowStyle() const;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    ArrowStyle m_arrowStyle;
};

// 5. 多边形 - 继承 QGraphicsPolygonItem 和 DrawingItemBase
class WhiteboardPolygonItem : public QGraphicsPolygonItem, public DrawingItemBase
{
public:
    explicit WhiteboardPolygonItem(QGraphicsItem *parent = nullptr);
    explicit WhiteboardPolygonItem(const QPolygonF &polygon, QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
};

// 6. 组合项 - 继承 QGraphicsItemGroup 和 DrawingItemBase
class WhiteboardGroupItem : public QGraphicsItemGroup, public DrawingItemBase
{
public:
    explicit WhiteboardGroupItem(QGraphicsItem *parent = nullptr);
    
    // 实现 DrawingItemBase 的纯虚函数
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    void setItemSelected(bool selected) override;
    bool isItemSelected() const override;
    
    // 组合项特有功能
    void showSelectionFrame(bool show);
    void updateControlPoints();
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_showSelectionFrame;
    QVector<QGraphicsRectItem*> m_controlPoints;
    QGraphicsRectItem *m_selectionFrame;
};
    explicit PenPathItem(QGraphicsItem *parent = nullptr);
    
    // DrawingItemBase 接口实现
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    // 自由绘图特有功能
    void addPoint(const QPointF &point);
    void smoothPath();
    
    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    QVector<QPointF> m_points;
};

// 2. 矩形 - 继承 QGraphicsRectItem 和 DrawingItemBase
class RectItem : public QGraphicsRectItem, public DrawingItemBase
{
public:
    explicit RectItem(QGraphicsItem *parent = nullptr);
    explicit RectItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    
    // DrawingItemBase 接口实现
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    // 矩形特有功能
    void setCornerRadius(qreal radius);
    qreal cornerRadius() const;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    qreal m_cornerRadius;
};

// 3. 椭圆/圆形 - 使用 QGraphicsEllipseItem
class EllipseItem : public QGraphicsEllipseItem
{
public:
    explicit EllipseItem(QGraphicsItem *parent = nullptr);
    explicit EllipseItem(const QRectF &rect, QGraphicsItem *parent = nullptr);
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_antiAliasing;
};

// 4. 直线 - 使用 QGraphicsLineItem
class LineItem : public QGraphicsLineItem
{
public:
    explicit LineItem(QGraphicsItem *parent = nullptr);
    explicit LineItem(const QLineF &line, QGraphicsItem *parent = nullptr);
    
    // 支持箭头
    void setArrowStyle(ArrowStyle style);
    ArrowStyle arrowStyle() const;
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    enum ArrowStyle { NoArrow, StartArrow, EndArrow, BothArrows };
    ArrowStyle m_arrowStyle;
    bool m_antiAliasing;
};

// 5. 多边形 - 使用 QGraphicsPolygonItem
class WhiteboardPolygonItem : public QGraphicsPolygonItem
{
public:
    explicit WhiteboardPolygonItem(QGraphicsItem *parent = nullptr);
    explicit WhiteboardPolygonItem(const QPolygonF &polygon, QGraphicsItem *parent = nullptr);
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_antiAliasing;
};

// 6. 组合项 - 使用 QGraphicsItemGroup
class WhiteboardGroupItem : public QGraphicsItemGroup
{
public:
    explicit WhiteboardGroupItem(QGraphicsItem *parent = nullptr);
    
    // 选择框和控制点
    void showSelectionFrame(bool show);
    void updateControlPoints();
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_showSelectionFrame;
    QVector<QGraphicsRectItem*> m_controlPoints;
    QGraphicsRectItem *m_selectionFrame;
};

// 7. 文本项 - 使用 QGraphicsTextItem（预留扩展）
class WhiteboardTextItem : public QGraphicsTextItem
{
public:
    explicit WhiteboardTextItem(QGraphicsItem *parent = nullptr);
    explicit WhiteboardTextItem(const QString &text, QGraphicsItem *parent = nullptr);
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_antiAliasing;
};

// 8. 图像项 - 使用 QGraphicsPixmapItem（预留扩展）
class WhiteboardPixmapItem : public QGraphicsPixmapItem
{
public:
    explicit WhiteboardPixmapItem(QGraphicsItem *parent = nullptr);
    explicit WhiteboardPixmapItem(const QPixmap &pixmap, QGraphicsItem *parent = nullptr);
    
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    bool m_antiAliasing;
};
```

### 5. ToolManager (工具管理器)

```cpp
class ToolManager : public QObject
{
    Q_OBJECT

public:
    explicit ToolManager(QObject *parent = nullptr);
    ~ToolManager();

    void setCurrentTool(WhiteboardTypes::DrawingType tool);
    WhiteboardTypes::DrawingType currentTool() const;
    
    BaseTool* getCurrentToolInstance() const;

    // 工具事件处理
    void handleMousePress(QGraphicsSceneMouseEvent *event);
    void handleMouseMove(QGraphicsSceneMouseEvent *event);
    void handleMouseRelease(QGraphicsSceneMouseEvent *event);
    
    // 多指绘制事件处理
    void handleTouchPress(int touchId, const QPointF &scenePos);
    void handleTouchMove(int touchId, const QPointF &scenePos);
    void handleTouchRelease(int touchId, const QPointF &scenePos);
    
    // 多指绘制支持查询
    bool supportsMultiTouch(WhiteboardTypes::DrawingType tool) const;

signals:
    void toolChanged(WhiteboardTypes::DrawingType tool);
    void itemCreated(DrawingItem *item);

private:
    WhiteboardTypes::DrawingType m_currentTool;
    std::map<WhiteboardTypes::DrawingType, std::unique_ptr<BaseTool>> m_tools;
    
    // 多指绘制状态管理
    QMap<int, DrawingItem*> m_activeTouchDrawings;  // 每个触摸点对应的绘图项
};
```

### 6. CommandManager (命令管理器)

```cpp
class CommandManager : public QObject
{
    Q_OBJECT

public:
    explicit CommandManager(QObject *parent = nullptr);
    ~CommandManager();

    void executeCommand(std::unique_ptr<UndoCommand> command);
    void undo();
    void redo();
    
    bool canUndo() const;
    bool canRedo() const;
    
    void clear();

signals:
    void canUndoChanged(bool canUndo);
    void canRedoChanged(bool canRedo);

private:
    std::vector<std::unique_ptr<UndoCommand>> m_undoStack;
    std::vector<std::unique_ptr<UndoCommand>> m_redoStack;
    size_t m_maxStackSize;
};
```

## 图元选择和变换系统

### 多选和Group操作设计

基于需求4的更新，系统需要支持单选和多选图元，并将多选的图元组合成临时group进行统一变换操作。

#### 选择管理器 (SelectionManager)

```cpp
class SelectionManager : public QObject
{
    Q_OBJECT

public:
    explicit SelectionManager(QObject *parent = nullptr);
    ~SelectionManager();

    // 选择操作
    void selectItem(QGraphicsItem *item, bool multiSelect = false);
    void selectItems(const QList<QGraphicsItem*> &items);
    void clearSelection();
    void toggleItemSelection(QGraphicsItem *item);
    
    // 选择状态查询
    QList<QGraphicsItem*> selectedItems() const;
    bool hasSelection() const;
    bool isMultiSelection() const;
    
    // Group 管理
    QGraphicsItemGroup* createTemporaryGroup();
    void destroyTemporaryGroup();
    QGraphicsItemGroup* currentGroup() const;
    
    // 变换控制
    void showTransformControls(bool show);
    void updateTransformControls();

signals:
    void selectionChanged();
    void groupCreated(QGraphicsItemGroup *group);
    void groupDestroyed();

private:
    void createSelectionFrame();
    void updateSelectionFrame();
    void createControlPoints();
    void updateControlPoints();
    
    QList<QGraphicsItem*> m_selectedItems;
    QGraphicsItemGroup *m_temporaryGroup;
    QGraphicsRectItem *m_selectionFrame;
    QVector<TransformControlPoint*> m_controlPoints;
    bool m_showControls;
};
```

#### 变换控制点 (TransformControlPoint)

```cpp
class TransformControlPoint : public QGraphicsRectItem
{
public:
    enum ControlType {
        TopLeft, TopCenter, TopRight,
        MiddleLeft, MiddleRight,
        BottomLeft, BottomCenter, BottomRight,
        RotationHandle
    };

    explicit TransformControlPoint(ControlType type, QGraphicsItem *parent = nullptr);
    
    ControlType controlType() const;
    void updatePosition(const QRectF &bounds);
    
    // 变换操作
    void handleTransform(const QPointF &delta, Qt::KeyboardModifiers modifiers);

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent *event) override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    ControlType m_type;
    QPointF m_startPos;
    QRectF m_originalBounds;
    bool m_isTransforming;
};
```

#### Group项实现 (WhiteboardGroupItem)

```cpp
class WhiteboardGroupItem : public QGraphicsItemGroup, public DrawingItemBase
{
public:
    explicit WhiteboardGroupItem(QGraphicsItem *parent = nullptr);
    ~WhiteboardGroupItem();
    
    // DrawingItemBase 接口实现
    QJsonObject toJson() const override;
    bool fromJson(const QJsonObject &json) override;
    
    void setPenColor(const QColor &color) override;
    QColor penColor() const override;
    
    void setPenWidth(qreal width) override;
    qreal penWidth() const override;
    
    void setBrushColor(const QColor &color) override;
    QColor brushColor() const override;
    
    // Group 特有功能
    void addItemToGroup(QGraphicsItem *item);
    void removeItemFromGroup(QGraphicsItem *item);
    QList<QGraphicsItem*> groupItems() const;
    
    // 变换控制
    void showSelectionFrame(bool show);
    void updateSelectionFrame();
    void setTransformControlsVisible(bool visible);
    
    // 临时Group标记
    void setTemporary(bool temporary);
    bool isTemporary() const;
    
    // 解散Group
    void disbandGroup();

protected:
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
    QVariant itemChange(GraphicsItemChange change, const QVariant &value) override;

private:
    bool m_showSelectionFrame;
    bool m_isTemporary;
    QGraphicsRectItem *m_selectionFrame;
    QVector<TransformControlPoint*> m_controlPoints;
    
    void updateControlPoints();
    void createControlPoints();
    void destroyControlPoints();
};
```

### 选择和变换工作流程

#### 单选操作流程

```mermaid
sequenceDiagram
    participant User
    participant Scene as WhiteboardScene
    participant SM as SelectionManager
    participant Item as GraphicsItem
    participant CP as ControlPoints

    User->>Scene: 点击图元
    Scene->>SM: selectItem(item)
    SM->>Item: 设置选中状态
    SM->>CP: 创建变换控制点
    CP->>User: 显示控制点和选择框
    
    User->>CP: 拖拽控制点
    CP->>Item: 应用变换
    Item->>Scene: 更新显示
```

#### 多选操作流程

```mermaid
sequenceDiagram
    participant User
    participant Scene as WhiteboardScene
    participant SM as SelectionManager
    participant Group as WhiteboardGroupItem
    participant CP as ControlPoints

    User->>Scene: Ctrl+点击多个图元
    Scene->>SM: selectItem(item, multiSelect=true)
    SM->>SM: 添加到选择列表
    
    alt 选择完成
        SM->>Group: createTemporaryGroup()
        Group->>Group: 添加选中图元到group
        SM->>CP: 为group创建控制点
        CP->>User: 显示group的统一控制界面
    end
    
    User->>CP: 操作group控制点
    CP->>Group: 应用变换到整个group
    Group->>Group: 同步变换到所有子图元
    
    User->>Scene: 点击空白区域取消选择
    Scene->>SM: clearSelection()
    SM->>Group: disbandGroup()
    Group->>Group: 恢复原始图元状态
```

### 变换操作类型

#### 平移变换
- **触发**: 拖拽图元或group的中心区域
- **实现**: 直接修改图元的position
- **Group处理**: 同时移动所有子图元

#### 缩放变换
- **触发**: 拖拽角落或边缘的缩放控制点
- **实现**: 修改图元的scale变换
- **约束**: 最小/最大缩放比例限制
- **Group处理**: 以group中心为基准进行缩放

#### 旋转变换
- **触发**: 拖拽旋转控制点（通常在顶部中心外侧）
- **实现**: 修改图元的rotation变换
- **Group处理**: 以group中心为旋转轴

### 变换约束和限制

```cpp
struct TransformConstraints {
    qreal minScale = 0.1;      // 最小缩放比例
    qreal maxScale = 10.0;     // 最大缩放比例
    bool maintainAspectRatio = false;  // 是否保持宽高比
    bool snapToGrid = false;   // 是否对齐网格
    qreal snapAngle = 15.0;    // 旋转角度吸附（度）
    
    // 边界限制
    QRectF boundingRect;       // 允许的移动范围
    bool constrainToBounds = false;
};
```

## Data Models

### 统一绘图数据结构

```cpp
// 统一的绘图数据结构，支持所有绘图类型
struct DrawingData {
    WhiteboardTypes::DrawingType type;
    QVector<QPointF> points;        // 路径点或形状关键点
    QPen pen;                       // 画笔样式
    QBrush brush;                   // 填充样式
    QRectF bounds;                  // 边界矩形
    qreal smoothness;               // 平滑度（用于路径）
    QVector<int> childItems;        // 子项索引（用于组合）
    QJsonObject metadata;           // 扩展元数据
    
    // JSON 序列化支持
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject &json);
    
    // 辅助函数
    bool isValid() const;
    void clear();
};

// 场景数据结构
struct SceneData {
    QVector<DrawingData> items;
    QRectF sceneRect;
    QJsonObject settings;
    
    // JSON 序列化支持
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject &json);
};
```

### 工具状态数据

```cpp
struct ToolState {
    WhiteboardTypes::DrawingType currentTool;
    QColor penColor;
    qreal penWidth;
    QColor brushColor;
    bool antiAliasing;
    qreal smoothness;
    
    // JSON 序列化支持
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject &json);
};
```

## Error Handling

### 错误处理策略

1. **资源管理错误**
   - 使用 RAII 和智能指针管理内存
   - 在析构函数中确保资源正确释放
   - 对大量图形项进行批量操作时检查内存使用

2. **用户输入错误**
   - 验证鼠标事件的有效性
   - 处理无效的工具切换请求
   - 对超出边界的操作进行限制

3. **文件操作错误**
   - 保存/加载场景时的文件 I/O 错误处理
   - 提供错误信息反馈机制
   - 实现数据备份和恢复机制

4. **性能相关错误**
   - 监控撤销栈大小，防止内存溢出
   - 对复杂路径进行简化处理
   - 实现视图区域裁剪优化

### 错误处理实现

```cpp
class ErrorHandler : public QObject
{
    Q_OBJECT

public:
    enum ErrorType {
        MemoryError,
        FileError,
        ValidationError,
        PerformanceError
    };

    static void handleError(ErrorType type, const QString &message);
    static void setErrorCallback(std::function<void(ErrorType, const QString&)> callback);

signals:
    void errorOccurred(ErrorType type, const QString &message);

private:
    static std::function<void(ErrorType, const QString&)> s_errorCallback;
};
```

## 抗锯齿渲染策略

### 抗锯齿实现原则

1. **统一处理策略**
   - 所有绘图项统一使用相同的抗锯齿设置
   - 确保在不同屏幕分辨率和 DPI 下的一致性
   - 特别优化 4K 屏幕低 DPI 环境下的显示效果

2. **性能平衡**
   - 在保证性能的前提下最大化抗锯齿效果
   - 根据图形项复杂度动态调整抗锯齿级别
   - 对频繁重绘的区域进行优化

### 抗锯齿实现细节

```cpp
class AntiAliasingManager : public QObject
{
    Q_OBJECT

public:
    enum AntiAliasingLevel {
        None,           // 无抗锯齿
        Basic,          // 基础抗锯齿
        High,           // 高质量抗锯齿
        Adaptive        // 自适应抗锯齿
    };

    static AntiAliasingManager* instance();
    
    void setupPainter(QPainter *painter, AntiAliasingLevel level = Adaptive) const;
    AntiAliasingLevel getOptimalLevel(const QRectF &itemBounds, qreal devicePixelRatio) const;
    
    // 屏幕适配
    void updateForScreen(const QScreen *screen);
    bool isHighDPIScreen() const;
    qreal getEffectiveDevicePixelRatio() const;

private:
    AntiAliasingManager();
    qreal m_devicePixelRatio;
    bool m_isHighDPI;
    AntiAliasingLevel m_globalLevel;
};

// DrawingItem 中的抗锯齿设置
void DrawingItem::setupAntiAliasing(QPainter *painter) const
{
    if (m_antiAliasing) {
        auto level = AntiAliasingManager::instance()->getOptimalLevel(
            boundingRect(), 
            painter->device()->devicePixelRatio()
        );
        AntiAliasingManager::instance()->setupPainter(painter, level);
    }
}
```

### 屏幕适配策略

1. **4K 屏幕低 DPI 优化**
   - 检测屏幕分辨率和 DPI 设置
   - 对低 DPI 的 4K 屏幕启用更强的抗锯齿
   - 使用子像素渲染技术改善线条质量

2. **统一显示效果**
   - 建立标准的渲染质量基准
   - 根据硬件能力自动调整渲染参数
   - 确保在不同设备上的视觉一致性

## 多指绘制支持

### 多指绘制设计原则

1. **互不干扰原则**
   - 每个触摸点独立创建绘图项
   - 不同触摸点的绘制操作完全隔离
   - 支持同时进行多个绘制操作

2. **工具支持策略**
   - 目前仅自由绘制（Pen工具）支持多指绘制
   - 其他工具保持单点操作模式
   - 预留扩展接口供未来添加更多多指支持工具

### 多指绘制实现架构

```mermaid
sequenceDiagram
    participant User
    participant View as WhiteboardView
    participant Scene as WhiteboardScene
    participant Tool as ToolManager
    participant Item1 as DrawingItem1
    participant Item2 as DrawingItem2

    User->>View: 多指触摸开始
    View->>View: 识别多个触摸点
    
    par 触摸点1处理
        View->>Scene: 转发触摸点1事件
        Scene->>Tool: handleTouchPress(touchId1, pos1)
        Tool->>Item1: 创建绘图项1
    and 触摸点2处理
        View->>Scene: 转发触摸点2事件
        Scene->>Tool: handleTouchPress(touchId2, pos2)
        Tool->>Item2: 创建绘图项2
    end
    
    User->>View: 多指移动
    
    par 触摸点1移动
        View->>Scene: 转发触摸点1移动
        Scene->>Tool: handleTouchMove(touchId1, newPos1)
        Tool->>Item1: 更新绘图项1
    and 触摸点2移动
        View->>Scene: 转发触摸点2移动
        Scene->>Tool: handleTouchMove(touchId2, newPos2)
        Tool->>Item2: 更新绘图项2
    end
```

### 多指绘制核心接口

```cpp
// BaseTool 基类中的多指支持接口
class BaseTool : public QObject
{
    Q_OBJECT

public:
    // 查询工具是否支持多指操作
    virtual bool supportsMultiTouch() const { return false; }
    
    // 多指事件处理接口
    virtual void onTouchPress(int touchId, const QPointF &scenePos) {}
    virtual void onTouchMove(int touchId, const QPointF &scenePos) {}
    virtual void onTouchRelease(int touchId, const QPointF &scenePos) {}

protected:
    // 多指状态管理辅助
    QMap<int, QVariant> m_touchStates;  // 每个触摸点的状态数据
};

// PenTool 中的多指绘制实现
class PenTool : public BaseTool
{
    Q_OBJECT

public:
    bool supportsMultiTouch() const override { return true; }
    
    void onTouchPress(int touchId, const QPointF &scenePos) override;
    void onTouchMove(int touchId, const QPointF &scenePos) override;
    void onTouchRelease(int touchId, const QPointF &scenePos) override;

private:
    QMap<int, PathItem*> m_activePathItems;  // 每个触摸点对应的路径项
};
```

### 触摸点状态管理

```cpp
// 触摸点状态数据结构
struct TouchPointState {
    int touchId;
    QPointF startPos;
    QPointF currentPos;
    QPointF lastPos;
    qint64 startTime;
    DrawingItem* associatedItem;
    bool isActive;
    
    TouchPointState(int id, const QPointF &pos) 
        : touchId(id), startPos(pos), currentPos(pos), lastPos(pos)
        , startTime(QDateTime::currentMSecsSinceEpoch())
        , associatedItem(nullptr), isActive(true) {}
};

// WhiteboardView 中的触摸点管理
class WhiteboardView : public QGraphicsView
{
private:
    void handleMultiTouchDrawing(QTouchEvent *event)
    {
        const auto &touchPoints = event->touchPoints();
        
        for (const auto &touchPoint : touchPoints) {
            int touchId = touchPoint.id();
            QPointF scenePos = mapToScene(touchPoint.pos().toPoint());
            
            switch (touchPoint.state()) {
            case Qt::TouchPointPressed:
                if (m_toolManager->supportsMultiTouch(m_currentTool)) {
                    m_drawingTouchIds.insert(touchId);
                    m_activeTouchPoints[touchId] = scenePos;
                    // 转发给场景处理
                    static_cast<WhiteboardScene*>(scene())->handleTouchPress(touchId, scenePos);
                }
                break;
                
            case Qt::TouchPointMoved:
                if (m_drawingTouchIds.contains(touchId)) {
                    m_activeTouchPoints[touchId] = scenePos;
                    static_cast<WhiteboardScene*>(scene())->handleTouchMove(touchId, scenePos);
                }
                break;
                
            case Qt::TouchPointReleased:
                if (m_drawingTouchIds.contains(touchId)) {
                    static_cast<WhiteboardScene*>(scene())->handleTouchRelease(touchId, scenePos);
                    m_drawingTouchIds.remove(touchId);
                    m_activeTouchPoints.remove(touchId);
                }
                break;
            }
        }
    }
    
    QMap<int, QPointF> m_activeTouchPoints;  // 活跃的触摸点
    QSet<int> m_drawingTouchIds;             // 正在绘制的触摸点ID
    WhiteboardTypes::DrawingType m_currentTool;
    ToolManager *m_toolManager;
};
```

### 多指绘制性能优化

1. **触摸点过滤**
   - 过滤掉移动距离过小的触摸事件
   - 限制同时活跃的触摸点数量
   - 优化触摸事件的处理频率

2. **绘制优化**
   - 每个触摸点独立的绘制缓存
   - 增量更新机制，只重绘变化的区域
   - 合并相近的触摸点更新操作

3. **内存管理**
   - 及时清理已结束的触摸点状态
   - 使用对象池管理频繁创建的路径项
   - 监控多指绘制时的内存使用情况

## 工具与 Qt 图形项的对应关系

### 工具类型与图形项映射

```cpp
// 工具类型与对应的 Qt 图形项类型映射
namespace WhiteboardTypes {
    struct ToolItemMapping {
        DrawingType toolType;
        QString qtItemClassName;
        QString description;
    };
    
    static const QVector<ToolItemMapping> TOOL_ITEM_MAPPINGS = {
        {DrawingType::Pen, "QGraphicsPathItem", "自由绘图路径"},
        {DrawingType::Rectangle, "QGraphicsRectItem", "矩形（可带圆角）"},
        {DrawingType::Ellipse, "QGraphicsEllipseItem", "椭圆/圆形"},
        {DrawingType::Line, "QGraphicsLineItem", "直线（可带箭头）"},
        {DrawingType::Eraser, "QGraphicsPathItem", "橡皮擦路径（用于擦除）"},
        {DrawingType::Lasso, "QGraphicsPolygonItem", "套索选择区域"},
        {DrawingType::Group, "QGraphicsItemGroup", "组合图形项"}
    };
}
```

### 具体工具实现策略

```cpp
// 1. PenTool - 创建 QGraphicsPathItem
class PenTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        auto pathItem = new PenPathItem();
        pathItem->addPoint(event->scenePos());
        // 应用当前样式设置
        pathItem->setPen(getCurrentPen());
        // 添加到场景
        scene()->addItem(pathItem);
        m_currentPath = pathItem;
    }
    
    void onMouseMove(QGraphicsSceneMouseEvent *event) override {
        if (m_currentPath) {
            m_currentPath->addPoint(event->scenePos());
        }
    }
    
private:
    PenPathItem *m_currentPath;
};

// 2. RectangleTool - 创建 QGraphicsRectItem
class RectangleTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        m_startPoint = event->scenePos();
        m_rectItem = new RectItem();
        m_rectItem->setPen(getCurrentPen());
        m_rectItem->setBrush(getCurrentBrush());
        scene()->addItem(m_rectItem);
    }
    
    void onMouseMove(QGraphicsSceneMouseEvent *event) override {
        if (m_rectItem) {
            QRectF rect(m_startPoint, event->scenePos());
            m_rectItem->setRect(rect.normalized());
        }
    }
    
private:
    QPointF m_startPoint;
    RectItem *m_rectItem;
};

// 3. EllipseTool - 创建 QGraphicsEllipseItem
class EllipseTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        m_startPoint = event->scenePos();
        m_ellipseItem = new EllipseItem();
        m_ellipseItem->setPen(getCurrentPen());
        m_ellipseItem->setBrush(getCurrentBrush());
        scene()->addItem(m_ellipseItem);
    }
    
    void onMouseMove(QGraphicsSceneMouseEvent *event) override {
        if (m_ellipseItem) {
            QRectF rect(m_startPoint, event->scenePos());
            m_ellipseItem->setRect(rect.normalized());
        }
    }
    
private:
    QPointF m_startPoint;
    EllipseItem *m_ellipseItem;
};

// 4. LineTool - 创建 QGraphicsLineItem
class LineTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        m_startPoint = event->scenePos();
        m_lineItem = new LineItem();
        m_lineItem->setPen(getCurrentPen());
        scene()->addItem(m_lineItem);
    }
    
    void onMouseMove(QGraphicsSceneMouseEvent *event) override {
        if (m_lineItem) {
            QLineF line(m_startPoint, event->scenePos());
            m_lineItem->setLine(line);
        }
    }
    
private:
    QPointF m_startPoint;
    LineItem *m_lineItem;
};

// 5. EraserTool - 特殊处理，修改现有路径
class EraserTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        // 查找与橡皮擦路径相交的图形项
        QList<QGraphicsItem*> items = scene()->items(event->scenePos());
        for (auto item : items) {
            if (auto pathItem = dynamic_cast<PenPathItem*>(item)) {
                // 对路径进行擦除处理
                eraseFromPath(pathItem, event->scenePos());
            }
        }
    }
    
private:
    void eraseFromPath(PenPathItem *pathItem, const QPointF &erasePoint);
};

// 6. LassoTool - 创建 QGraphicsPolygonItem 进行选择
class LassoTool : public BaseTool
{
public:
    void onMousePress(QGraphicsSceneMouseEvent *event) override {
        m_lassoPoints.clear();
        m_lassoPoints.append(event->scenePos());
        
        m_lassoItem = new QGraphicsPolygonItem();
        QPen pen(Qt::DashLine);
        pen.setColor(Qt::blue);
        m_lassoItem->setPen(pen);
        scene()->addItem(m_lassoItem);
    }
    
    void onMouseMove(QGraphicsSceneMouseEvent *event) override {
        if (m_lassoItem) {
            m_lassoPoints.append(event->scenePos());
            QPolygonF polygon(m_lassoPoints);
            m_lassoItem->setPolygon(polygon);
        }
    }
    
    void onMouseRelease(QGraphicsSceneMouseEvent *event) override {
        if (m_lassoItem) {
            // 查找套索内的图形项
            QPolygonF polygon(m_lassoPoints);
            selectItemsInPolygon(polygon);
            
            // 移除套索显示
            scene()->removeItem(m_lassoItem);
            delete m_lassoItem;
            m_lassoItem = nullptr;
        }
    }
    
private:
    QVector<QPointF> m_lassoPoints;
    QGraphicsPolygonItem *m_lassoItem;
    
    void selectItemsInPolygon(const QPolygonF &polygon);
};
```

### Qt 图形项的优势

1. **性能优化**
   - Qt 内置的碰撞检测和边界计算
   - 优化的绘制和更新机制
   - 内存管理和缓存机制

2. **功能完整性**
   - 内置的变换支持（缩放、旋转、平移）
   - 选择和拖拽功能
   - Z-order 管理

3. **扩展性**
   - 可以轻松继承和扩展功能
   - 支持自定义绘制
   - 事件处理机制完善

4. **兼容性**
   - 与 QGraphicsView 框架完美集成
   - 支持各种 Qt 版本
   - 跨平台一致性

## 性能优化考虑

### 渲染优化
- 使用视图区域裁剪，只绘制可见区域的图形项
- 实现图形项的层次细节（LOD）系统
- 对复杂路径进行简化和缓存

### 内存优化
- 使用对象池管理频繁创建/销毁的图形项
- 实现智能的撤销栈大小限制
- 对大型场景进行分块管理

### 交互优化
- 实现增量更新机制，避免全场景重绘
- 使用异步处理复杂的几何计算
- 优化鼠标事件的处理频率