cmake_minimum_required(VERSION 3.16)

project(hl-whiteboard VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets)

set(PROJECT_SOURCES
        whiteboard.cpp
        whiteboard.h
        types/whiteboardtypes.h
        types/whiteboardtypes.cpp
        items/base/drawingitembase.h
        items/base/drawingitembase.cpp
        items/penpathitem.h
        items/penpathitem.cpp
        items/rectitem.h
        items/rectitem.cpp
        items/ellipseitem.h
        items/ellipseitem.cpp
        items/lineitem.h
        items/lineitem.cpp
        scene/whiteboardscene.h
        scene/whiteboardscene.cpp
        scene/scenemanager.h
        scene/scenemanager.cpp
        view/whiteboardview.h
        view/whiteboardview.cpp
        tools/toolmanager.h
        tools/toolmanager.cpp
        tools/base/basetool.h
        tools/base/basetool.cpp
        tools/pentool.h
        tools/pentool.cpp
        tools/rectangletool.h
        tools/rectangletool.cpp
        tools/ellipsetool.h
        tools/ellipsetool.cpp
        tools/linetool.h
        tools/linetool.cpp
        coordination/eventcoordinator.h
        coordination/eventcoordinator.cpp
        utils/DrawingPerformanceProfiler.h
        utils/DrawingPerformanceProfiler.cpp
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_library(hl-whiteboard STATIC
        ${PROJECT_SOURCES}
    )
else()
    add_library(hl-whiteboard STATIC
        ${PROJECT_SOURCES}
    )
endif()

target_link_libraries(hl-whiteboard PRIVATE Qt${QT_VERSION_MAJOR}::Widgets)

# Set library properties
set_target_properties(hl-whiteboard PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

include(GNUInstallDirs)
install(TARGETS hl-whiteboard
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)


