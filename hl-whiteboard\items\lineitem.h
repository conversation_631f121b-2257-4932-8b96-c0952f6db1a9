#ifndef LineItem_H
#define LineItem_H

#include <QGraphicsLineItem>
#include <QJsonObject>
#include <QLineF>
#include <QPointF>
#include "base/drawingitembase.h"

/**
 * @brief LineItem - 直线绘图项
 *
 * 继承自 QGraphicsLineItem 和 DrawingItemBase，实现直线绘制功能。
 * 支持抗锯齿绘制和 JSON 序列化。
 */
class LineItem : public QGraphicsLineItem, public DrawingItemBase
{
public:
    explicit LineItem(QGraphicsItem *parent = nullptr);
    explicit LineItem(const QLineF &line, QGraphicsItem *parent = nullptr);
    explicit LineItem(qreal x1, qreal y1, qreal x2, qreal y2, QGraphicsItem *parent = nullptr);
    ~LineItem();
    
    // 实现 DrawingItemBase 的几何序列化接口
    QJsonObject serializeGeometry() const override;
    bool deserializeGeometry(const QJsonObject &json) override;
    
    // 直线特有功能
    void setLine(const QLineF &line);
    void setLine(qreal x1, qreal y1, qreal x2, qreal y2);
    void setLine(const QPointF &p1, const QPointF &p2);
    QLineF lineF() const;

    // 端点操作
    void setStartPoint(const QPointF &point);
    QPointF startPoint() const;

    void setEndPoint(const QPointF &point);
    QPointF endPoint() const;

    // 直线属性
    qreal length() const;
    qreal angle() const;  // 角度（弧度）
    qreal angleDegrees() const;  // 角度（度）
    QPointF center() const;

    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;


private:
};

#endif // LineItem_H
