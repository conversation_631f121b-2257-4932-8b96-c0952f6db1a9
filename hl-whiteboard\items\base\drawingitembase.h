#ifndef DRAWINGITEMBASE_H
#define DRAWINGITEMBASE_H

#include <QJsonObject>
#include <QColor>
#include <QPainter>
#include <QGraphicsItem>
#include "../../types/whiteboardtypes.h"

// 绘图项统一基类接口 - 简单版本
class DrawingItemBase
{
public:
    explicit DrawingItemBase(WhiteboardTypes::DrawingType type, QGraphicsItem* item);
    virtual ~DrawingItemBase();

    // 类型信息
    WhiteboardTypes::DrawingType drawingType() const;

    // JSON 序列化支持 - 基类处理所有部分
    virtual QJsonObject toJson() const;
    virtual bool fromJson(const QJsonObject &json);

    // 子类需要实现的几何特定序列化
    virtual QJsonObject serializeGeometry() const = 0;
    virtual bool deserializeGeometry(const QJsonObject &json) = 0;

    // 抗锯齿支持
    void setAntiAliasing(bool enabled);
    bool antiAliasing() const;

    // 元数据管理
    void setMetadata(const QJsonObject &metadata);
    QJsonObject metadata() const;

protected:
    // 抗锯齿绘制辅助函数
    void setupAntiAliasing(QPainter *painter) const;

    // 通用初始化函数
    void initializeDrawingItem();

private:
    WhiteboardTypes::DrawingType m_type;
    bool m_antiAliasing;
    QJsonObject m_metadata;
    QGraphicsItem* m_item;  // 关联的图形项
};

#endif // DRAWINGITEMBASE_H
