#ifndef SCENEMANAGER_H
#define SCENEMANAGER_H

#include <QObject>
#include <QRectF>
#include <QPointF>
#include <QList>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>
#include "../types/whiteboardtypes.h"

class WhiteboardScene;
class QGraphicsItem;
class QGraphicsItemGroup;
class QGraphicsSceneMouseEvent;
class DrawingItemBase;

/**
 * @brief SceneManager - 场景状态和图形项生命周期管理器
 * 
 * 负责管理场景状态、图形项的生命周期、无限扩展场景支持，
 * 以及与其他管理器的协调机制
 */
class SceneManager : public QObject
{
    Q_OBJECT

public:
    explicit SceneManager(QObject *parent = nullptr);
    ~SceneManager();

    // 场景关联
    void setScene(WhiteboardScene *scene);
    WhiteboardScene* scene() const;

    // 图形项生命周期管理
    void registerItem(DrawingItemBase *item);
    void unregisterItem(DrawingItemBase *item);
    void cleanupDestroyedItems();
    
    QList<DrawingItemBase*> managedItems() const;
    int itemCount() const;
    int itemCount(WhiteboardTypes::DrawingType type) const;

    // 无限扩展场景支持
    void enableInfiniteScene(bool enable);
    bool isInfiniteSceneEnabled() const;
    
    void setSceneExpandMargin(qreal margin);
    qreal sceneExpandMargin() const;
    
    void expandSceneToInclude(const QRectF &rect);
    void expandSceneToInclude(const QPointF &point);
    void ensurePointVisible(const QPointF &point);
    
    QRectF currentSceneBounds() const;
    QRectF contentBounds() const;
    void optimizeSceneBounds();

    // 场景状态管理
    void saveSceneState();
    void restoreSceneState();
    void clearSceneState();
    
    bool hasUnsavedChanges() const;
    void markAsClean();
    void markAsDirty();

    // 内存和性能管理
    void setMaxItemCount(int maxCount);
    int maxItemCount() const;
    
    void enableAutoCleanup(bool enable);
    bool isAutoCleanupEnabled() const;
    
    void setCleanupInterval(int intervalMs);
    int cleanupInterval() const;
    
    void performCleanup();
    size_t estimateMemoryUsage() const;

    // 与其他管理器的协调机制（预留接口）
    void notifyToolManagerChange();
    
    void handleItemAdded(DrawingItemBase *item);
    void handleItemRemoved(DrawingItemBase *item);
    void handleItemModified(DrawingItemBase *item);

    // 场景数据管理
    QJsonObject exportSceneData() const;
    bool importSceneData(const QJsonObject &data);
    
    void clearAllItems();
    void resetScene();

    // 统计和调试信息
    struct SceneStatistics {
        int totalItems;
        int itemsByType[static_cast<int>(WhiteboardTypes::DrawingType::Group) + 1];
        qreal sceneBoundsArea;
        qreal contentBoundsArea;
        size_t estimatedMemoryUsage;
        bool hasUnsavedChanges;
    };
    
    SceneStatistics getStatistics() const;
    void printDebugInfo() const;

signals:
    // 场景状态变更信号
    void sceneStateChanged();
    void sceneBoundsChanged(const QRectF &newBounds);
    void contentBoundsChanged(const QRectF &newBounds);
    
    // 图形项生命周期信号
    void itemRegistered(DrawingItemBase *item);
    void itemUnregistered(DrawingItemBase *item);
    void itemsCleanedUp(int count);
    
    // 内存和性能信号
    void memoryUsageChanged(size_t usage);
    void cleanupPerformed();
    void maxItemCountReached();
    
    // 与其他管理器协调的信号
    void coordinationRequested(const QString &managerType);

private slots:
    void onCleanupTimer();
    void onSceneRectChanged();
    void onItemDestroyed();
    
    // 场景事件处理槽（用于无限扩展场景支持）
    void onSceneMousePress(QGraphicsSceneMouseEvent *event);
    void onSceneMouseMove(QGraphicsSceneMouseEvent *event);
    void onSceneMouseRelease(QGraphicsSceneMouseEvent *event);

private:
    // 初始化和清理
    void setupManager();
    void connectSignals();
    void disconnectSignals();
    
    // 场景边界管理
    void updateSceneBounds();
    void calculateOptimalBounds();
    QRectF calculateContentBounds() const;
    
    // 图形项管理辅助方法
    void addItemToRegistry(DrawingItemBase *item);
    void removeItemFromRegistry(DrawingItemBase *item);
    bool isItemValid(DrawingItemBase *item) const;
    
    // 内存管理辅助方法
    void checkMemoryLimits();
    void performMemoryOptimization();
    size_t calculateItemMemoryUsage(DrawingItemBase *item) const;
    
    // 状态管理辅助方法
    void updateSceneState();
    void validateSceneConsistency();

    // 私有成员变量
    WhiteboardScene *m_scene;
    
    // 图形项管理
    QList<DrawingItemBase*> m_managedItems;
    QList<DrawingItemBase*> m_pendingCleanup;
    
    // 无限扩展场景支持
    bool m_infiniteSceneEnabled;
    qreal m_sceneExpandMargin;
    QRectF m_lastSceneBounds;
    QRectF m_lastContentBounds;
    
    // 场景状态
    bool m_hasUnsavedChanges;
    QJsonObject m_savedSceneState;
    
    // 内存和性能管理
    int m_maxItemCount;
    bool m_autoCleanupEnabled;
    QTimer *m_cleanupTimer;
    size_t m_lastMemoryUsage;
    
    // 统计信息
    mutable SceneStatistics m_cachedStatistics;
    mutable bool m_statisticsValid;
    
    // 常量
    static const qreal DEFAULT_EXPAND_MARGIN;
    static const int DEFAULT_MAX_ITEM_COUNT;
    static const int DEFAULT_CLEANUP_INTERVAL;
    static const size_t MEMORY_WARNING_THRESHOLD;
    
    Q_DISABLE_COPY(SceneManager)
};

#endif // SCENEMANAGER_H
