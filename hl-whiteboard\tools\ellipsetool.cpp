#include "ellipsetool.h"
#include "../items/ellipseitem.h"
#include <QDebug>

EllipseTool::EllipseTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Ellipse, parent)
    , m_currentEllipse(nullptr)
{
}

EllipseTool::~EllipseTool()
{
    finishCurrentOperation();
}

void EllipseTool::onInputPress(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    m_isDrawing = true;
    m_startPoint = scenePos;

    // 创建新的椭圆项
    m_currentEllipse = createNewEllipse(m_startPoint);
}

void EllipseTool::onInputMove(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)

    if (!m_isDrawing || !m_currentEllipse) {
        return;
    }

    // 直接更新椭圆的矩形
    QRectF rect(m_startPoint, scenePos);
    m_currentEllipse->setRect(rect.normalized());
}

void EllipseTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    Q_UNUSED(inputId)
    Q_UNUSED(scenePos)

    if (m_isDrawing && m_currentEllipse) {
        finishEllipse();
    }

    m_isDrawing = false;
    m_currentEllipse = nullptr;
}

void EllipseTool::finishCurrentOperation()
{
    if (m_currentEllipse) {
        finishEllipse();
        m_currentEllipse = nullptr;
    }

    m_isDrawing = false;
    BaseTool::finishCurrentOperation();
}

void EllipseTool::cancelCurrentOperation()
{
    if (m_currentEllipse) {
        removeItemFromScene(m_currentEllipse);
        m_currentEllipse = nullptr;
    }

    m_isDrawing = false;
    BaseTool::cancelCurrentOperation();
}

EllipseItem* EllipseTool::createNewEllipse(const QPointF &startPoint)
{
    EllipseItem *ellipseItem = new EllipseItem();

    QRectF initialRect(startPoint, QSizeF(0, 0));
    ellipseItem->setRect(initialRect);

    addItemToScene(ellipseItem);

    return ellipseItem;
}

void EllipseTool::finishEllipse()
{
    if (m_currentEllipse) {
        emit itemFinished(m_currentEllipse);
    }
}


