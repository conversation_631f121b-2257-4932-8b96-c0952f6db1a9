#ifndef PENPATHITEM_H
#define PENPATHITEM_H

#include <QGraphicsPathItem>
#include <QVector>
#include <QPointF>
#include <QPainterPath>
#include <QJsonObject>
#include "base/drawingitembase.h"

/**
 * @brief PenPathItem - 自由绘图路径项
 * 
 * 继承自 QGraphicsPathItem 和 DrawingItemBase，实现自由绘制功能。
 * 支持路径点添加、平滑算法、抗锯齿绘制和 JSON 序列化。
 */
class PenPathItem : public QGraphicsPathItem, public DrawingItemBase
{
public:
    explicit PenPathItem(QGraphicsItem *parent = nullptr);
    ~PenPathItem();
    
    // 实现 DrawingItemBase 的几何序列化接口
    QJsonObject serializeGeometry() const override;
    bool deserializeGeometry(const QJsonObject &json) override;
    
    // 路径特有功能
    void addPoint(const QPointF &point);
    void smoothPath();
    void clearPath();
    
    // 路径信息查询
    QVector<QPointF> points() const;
    int pointCount() const;
    bool isEmpty() const;
    
    // 路径操作
    void setPoints(const QVector<QPointF> &points);
    void simplifyPath(qreal tolerance = 2.0);
    
    // 重写绘制以支持抗锯齿
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

    // 重写形状计算
    QPainterPath shape() const override;

protected:
    // 路径构建辅助函数
    void rebuildPath();

    // 平滑算法辅助函数
    QVector<QPointF> smoothPoints(const QVector<QPointF> &points, qreal smoothness = 0.5) const;
    QPointF calculateControlPoint(const QPointF &p1, const QPointF &p2, const QPointF &p3, qreal smoothness) const;


private:
    QVector<QPointF> m_points;          // 路径点集合
    qreal m_smoothness;                 // 平滑度参数 (0.0 - 1.0)

    // 性能优化参数
    static constexpr qreal MIN_POINT_DISTANCE = 1.0;  // 最小点间距离
};

#endif // PENPATHITEM_H
