#ifndef LINETOOL_H
#define LINETOOL_H

#include "base/basetool.h"
#include <QPointF>
#include <QLineF>

// 前向声明
class LineItem;

/**
 * @brief 直线工具类
 *
 * 实现直线绘制功能，支持拖拽创建直线。
 */
class LineTool : public BaseTool
{
    Q_OBJECT

public:
    explicit LineTool(QObject *parent = nullptr);
    ~LineTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;

private:
    // 直线创建和管理
    LineItem* createNewLine(const QPointF &startPoint);
    void finishLine();

private:
    // 绘制状态
    LineItem *m_currentLine;
    QPointF m_startPoint;
};

#endif // LINETOOL_H
