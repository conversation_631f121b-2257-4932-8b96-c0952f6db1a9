#ifndef WHITEBOARD_H
#define WHITEBOARD_H

#include <QWidget>
#include "types/whiteboardtypes.h"
#include "items/base/drawingitembase.h"

// 前向声明
class WhiteboardPrivate;
class WhiteboardView;
class WhiteboardScene;
class ToolManager;
class SceneManager;

class Whiteboard : public QWidget
{
    Q_OBJECT

public:
    explicit Whiteboard(QWidget *parent = nullptr);
    ~Whiteboard();

    // 工具管理接口
    void setCurrentTool(WhiteboardTypes::DrawingType tool);
    WhiteboardTypes::DrawingType currentTool() const;

    // 样式设置接口
    void setPenColor(const QColor &color);
    QColor penColor() const;
    
    void setPenWidth(qreal width);
    qreal penWidth() const;
    
    void setBrushColor(const QColor &color);
    QColor brushColor() const;

    // 撤销重做接口
    void undo();
    void redo();
    bool canUndo() const;
    bool canRedo() const;



    // 场景管理接口
    void clearScene();
    void saveScene(const QString &filename);
    void loadScene(const QString &filename);
    
    // JSON 导入导出接口
    QJsonObject exportToJson() const;
    bool importFromJson(const QJsonObject &json);

    // 抗锯齿设置
    void setAntiAliasing(bool enabled);
    bool antiAliasing() const;

    // 多指绘制支持
    void setMultiTouchEnabled(bool enabled);
    bool multiTouchEnabled() const;

    // 交互模式管理
    enum class InteractionMode {
        Drawing,    // 绘图模式
        Selection   // 选择模式
    };

    void setInteractionMode(InteractionMode mode);
    InteractionMode interactionMode() const;

    // 组件访问接口（用于高级控制）
    WhiteboardView* view() const;
    WhiteboardScene* scene() const;
    ToolManager* toolManager() const;
    SceneManager* sceneManager() const;

signals:
    // 工具变更信号
    void toolChanged(WhiteboardTypes::DrawingType tool);
    
    // 撤销重做状态变更信号
    void canUndoChanged(bool canUndo);
    void canRedoChanged(bool canRedo);
    
    // 场景变更信号
    void sceneChanged();

private:
    // Pimpl 模式 - 隐藏内部实现
    WhiteboardPrivate *d_ptr;
    Q_DECLARE_PRIVATE(Whiteboard)
    Q_DISABLE_COPY(Whiteboard)
};
#endif // WHITEBOARD_H
