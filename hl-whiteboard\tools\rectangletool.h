#ifndef RECTANGLETOOL_H
#define RECTANGLETOOL_H

#include "base/basetool.h"
#include <QPointF>
#include <QRectF>

// 前向声明
class RectItem;

/**
 * @brief 矩形工具类
 *
 * 实现矩形绘制功能，支持拖拽创建矩形。
 */
class RectangleTool : public BaseTool
{
    Q_OBJECT

public:
    explicit RectangleTool(QObject *parent = nullptr);
    ~RectangleTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;

private:
    // 矩形创建和管理
    RectItem* createNewRectangle(const QPointF &startPoint);
    void finishRectangle();

private:
    // 绘制状态
    RectItem *m_currentRect;
    QPointF m_startPoint;
};

#endif // RECTANGLETOOL_H
