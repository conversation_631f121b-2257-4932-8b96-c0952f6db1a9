#ifndef ELLIPSETOOL_H
#define ELLIPSETOOL_H

#include "base/basetool.h"
#include <QPointF>
#include <QRectF>

// 前向声明
class EllipseItem;

/**
 * @brief 椭圆工具类
 */
class EllipseTool : public BaseTool
{
    Q_OBJECT

public:
    explicit EllipseTool(QObject *parent = nullptr);
    ~EllipseTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;



private:
    // 椭圆创建和管理
    EllipseItem* createNewEllipse(const QPointF &startPoint);
    void finishEllipse();

private:
    // 绘制状态
    EllipseItem *m_currentEllipse;
    QPointF m_startPoint;
};

#endif // ELLIPSETOOL_H
