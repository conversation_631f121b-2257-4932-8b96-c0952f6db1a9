#include "whiteboardview.h"
#include "../scene/whiteboardscene.h"
#include "../coordination/eventcoordinator.h"
#include "../types/whiteboardtypes.h"
#include <QGraphicsScene>
#include <QScrollBar>
#include <QPainter>
#include <QGestureEvent>
#include <QPinchGesture>
#include <QPanGesture>
#include <QTouchEvent>
#include <QApplication>
#include <QDebug>
#include <cmath>

WhiteboardView::WhiteboardView(QWidget *parent)
    : QGraphicsView(parent)
    , m_drawingMode(true)
    , m_gridVisible(false)
    , m_gridSize(20.0)
    , m_antiAliasing(true)
    , m_panning(false)
    , m_eventCoordinator(nullptr)
{
    setupView();
}

WhiteboardView::WhiteboardView(QGraphicsScene *scene, QWidget *parent)
    : QGraphicsView(scene, parent)
    , m_drawingMode(true)
    , m_gridVisible(false)
    , m_gridSize(20.0)
    , m_antiAliasing(true)
    , m_panning(false)
    , m_eventCoordinator(nullptr)
{
    setupView();
    if (scene) {
        connect(scene, &QGraphicsScene::sceneRectChanged,
                this, &WhiteboardView::onSceneRectChanged);
    }
}

WhiteboardView::~WhiteboardView()
{
}

void WhiteboardView::setEventCoordinator(EventCoordinator *coordinator)
{
    m_eventCoordinator = coordinator;
}

EventCoordinator* WhiteboardView::eventCoordinator() const
{
    return m_eventCoordinator;
}

void WhiteboardView::setupView()
{
    // 基本视图设置
    setDragMode(QGraphicsView::NoDrag);
    setRenderHint(QPainter::Antialiasing, m_antiAliasing);
    setRenderHint(QPainter::SmoothPixmapTransform, true);
    setRenderHint(QPainter::TextAntialiasing, true);
    
    // 视图更新优化
    setViewportUpdateMode(QGraphicsView::SmartViewportUpdate);
    setCacheMode(QGraphicsView::CacheBackground);
    
    // 滚动条设置
    setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    
    // 变换锚点设置
    setTransformationAnchor(QGraphicsView::AnchorUnderMouse);
    setResizeAnchor(QGraphicsView::AnchorViewCenter);
    
    // 启用手势识别（由外部控制）
    // grabGesture(Qt::PinchGesture);
    // grabGesture(Qt::PanGesture);
}

// 基本视图控制实现
void WhiteboardView::fitToWindow()
{
    if (!scene()) return;
    
    QRectF sceneRect = scene()->itemsBoundingRect();
    if (sceneRect.isEmpty()) return;
    
    fitInView(sceneRect, Qt::KeepAspectRatio);
    emitViewTransformed();
}

void WhiteboardView::centerContent()
{
    if (!scene()) return;
    
    QRectF sceneRect = scene()->itemsBoundingRect();
    if (!sceneRect.isEmpty()) {
        centerOn(sceneRect.center());
        emitViewTransformed();
    }
}

// 平移控制实现
void WhiteboardView::panTo(const QPointF &scenePos)
{
    centerOn(scenePos);
    emitViewTransformed();
}

void WhiteboardView::panBy(const QPointF &delta)
{
    QScrollBar *hBar = horizontalScrollBar();
    QScrollBar *vBar = verticalScrollBar();
    
    hBar->setValue(hBar->value() + static_cast<int>(delta.x()));
    vBar->setValue(vBar->value() + static_cast<int>(delta.y()));
    
    emitViewTransformed();
}

void WhiteboardView::centerOnPoint(const QPointF &scenePos)
{
    centerOn(scenePos);
    emitViewTransformed();
}

// 视图模式设置
void WhiteboardView::setDragMode(QGraphicsView::DragMode mode)
{
    QGraphicsView::setDragMode(mode);
    m_drawingMode = (mode == QGraphicsView::NoDrag);
    emit interactionModeChanged(m_drawingMode);
}

void WhiteboardView::setInteractionMode(bool drawingMode)
{
    if (m_drawingMode == drawingMode) return;
    
    m_drawingMode = drawingMode;
    
    if (drawingMode) {
        setDragMode(QGraphicsView::NoDrag);
    } else {
        setDragMode(QGraphicsView::RubberBandDrag);
    }
    
    emit interactionModeChanged(m_drawingMode);
}

bool WhiteboardView::isDrawingMode() const
{
    return m_drawingMode;
}

// 网格和背景设置
void WhiteboardView::setGridVisible(bool visible)
{
    if (m_gridVisible == visible) return;
    
    m_gridVisible = visible;
    invalidateScene();
    viewport()->update();
}

bool WhiteboardView::isGridVisible() const
{
    return m_gridVisible;
}

void WhiteboardView::setGridSize(qreal size)
{
    if (qAbs(m_gridSize - size) < 0.001) return;
    
    m_gridSize = qMax(1.0, size);
    if (m_gridVisible) {
        invalidateScene();
        viewport()->update();
    }
}

qreal WhiteboardView::gridSize() const
{
    return m_gridSize;
}

// 抗锯齿设置
void WhiteboardView::setAntiAliasing(bool enabled)
{
    if (m_antiAliasing == enabled) return;
    
    m_antiAliasing = enabled;
    setRenderHint(QPainter::Antialiasing, enabled);
    viewport()->update();
}

bool WhiteboardView::antiAliasing() const
{
    return m_antiAliasing;
}



// 视图状态查询
QRectF WhiteboardView::visibleSceneRect() const
{
    return QGraphicsView::mapToScene(viewport()->rect()).boundingRect();
}

QPointF WhiteboardView::mapToScene(const QPoint &viewPos) const
{
    return QGraphicsView::mapToScene(viewPos);
}

QPoint WhiteboardView::mapFromScene(const QPointF &scenePos) const
{
    return QGraphicsView::mapFromScene(scenePos);
}

// 私有辅助方法

void WhiteboardView::emitViewTransformed()
{
    emit viewTransformed();
}

void WhiteboardView::onSceneRectChanged(const QRectF &rect)
{
    Q_UNUSED(rect)
    // 场景矩形变化时的处理
    emitViewTransformed();
}
// 事件处理实现

void WhiteboardView::mousePressEvent(QMouseEvent *event)
{
    QPointF scenePos = mapToScene(event->pos());

    if (event->button() == Qt::MiddleButton) {
        // 中键开始平移
        m_panning = true;
        m_lastPanPoint = event->pos();
        setCursor(Qt::ClosedHandCursor);
        event->accept();
        return;
    }

    emit sceneClicked(scenePos, event->button());

    // 让 QGraphicsView 处理事件，它会转换为 QGraphicsSceneMouseEvent 并传递给 Scene
    // 这样避免了重复处理
    QGraphicsView::mousePressEvent(event);
}

void WhiteboardView::mouseMoveEvent(QMouseEvent *event)
{
    if (m_panning) {
        // 处理平移
        QPoint delta = event->pos() - m_lastPanPoint;
        panBy(QPointF(-delta.x(), -delta.y()));
        m_lastPanPoint = event->pos();
        event->accept();
        return;
    }
    
    QPointF scenePos = mapToScene(event->pos());
    emit sceneDragged(scenePos, event->buttons());
    
    QGraphicsView::mouseMoveEvent(event);
}

void WhiteboardView::mouseReleaseEvent(QMouseEvent *event)
{
    if (m_panning && (event->button() == Qt::MiddleButton || 
        (event->button() == Qt::LeftButton && m_panning))) {
        // 结束平移
        m_panning = false;
        setCursor(Qt::ArrowCursor);
        event->accept();
        return;
    }
    
    QGraphicsView::mouseReleaseEvent(event);
}

void WhiteboardView::mouseDoubleClickEvent(QMouseEvent *event)
{
    QPointF scenePos = mapToScene(event->pos());
    emit sceneDoubleClicked(scenePos);
    
    QGraphicsView::mouseDoubleClickEvent(event);
}



void WhiteboardView::resizeEvent(QResizeEvent *event)
{
    QGraphicsView::resizeEvent(event);
    emitViewTransformed();
}

void WhiteboardView::paintEvent(QPaintEvent *event)
{
    QGraphicsView::paintEvent(event);
}

// 事件处理
bool WhiteboardView::event(QEvent *event)
{
    // 处理触摸事件（用于多指绘制）
    if (event->type() == QEvent::TouchBegin ||
        event->type() == QEvent::TouchUpdate ||
        event->type() == QEvent::TouchEnd) {
        return touchEvent(static_cast<QTouchEvent*>(event));
    }

    // 处理手势事件（用于缩放和平移）
    if (event->type() == QEvent::Gesture) {
        return gestureEvent(static_cast<QGestureEvent*>(event));
    }

    return QGraphicsView::event(event);
}

// 触摸事件处理
bool WhiteboardView::touchEvent(QTouchEvent *event)
{
    // 获取场景引用
    WhiteboardScene *whiteboardScene = qobject_cast<WhiteboardScene*>(scene());
    if (!whiteboardScene) {
        qDebug() << "Scene is not a WhiteboardScene, touch events not supported";
        return false;
    }

    // 处理每个触摸点，通过场景层统一处理
    const auto &touchPoints = event->touchPoints();
    for (const auto &touchPoint : touchPoints) {
        int touchId = touchPoint.id();
        QPointF scenePos = mapToScene(touchPoint.pos().toPoint());

        switch (touchPoint.state()) {
        case Qt::TouchPointPressed:
            whiteboardScene->handleTouchPress(touchId, scenePos);
            break;

        case Qt::TouchPointMoved:
            whiteboardScene->handleTouchMove(touchId, scenePos);
            break;

        case Qt::TouchPointReleased:
            whiteboardScene->handleTouchRelease(touchId, scenePos);
            break;

        case Qt::TouchPointStationary:
            // 静止的触摸点不需要处理
            break;
        }
    }

    // 接受事件，防止转换为鼠标事件
    event->accept();
    return true;
}



bool WhiteboardView::gestureEvent(QGestureEvent *event)
{
    if (QGesture *pan = event->gesture(Qt::PanGesture)) {
        panTriggered(static_cast<QPanGesture*>(pan));
    }
    return true;
}

void WhiteboardView::panTriggered(QPanGesture *gesture)
{
    QPointF delta = gesture->delta();
    panBy(-delta);  // 反向平移以匹配手势方向
}

// 绘制网格背景
void WhiteboardView::drawBackground(QPainter *painter, const QRectF &rect)
{
    QGraphicsView::drawBackground(painter, rect);
    
    if (!m_gridVisible) return;
    
    painter->save();
    painter->setPen(QPen(QColor(200, 200, 200), 0));
    
    // 计算网格线的起始和结束位置
    qreal left = int(rect.left()) - (int(rect.left()) % int(m_gridSize));
    qreal top = int(rect.top()) - (int(rect.top()) % int(m_gridSize));
    
    QVarLengthArray<QLineF, 100> lines;
    
    // 绘制垂直网格线
    for (qreal x = left; x < rect.right(); x += m_gridSize) {
        lines.append(QLineF(x, rect.top(), x, rect.bottom()));
    }
    
    // 绘制水平网格线
    for (qreal y = top; y < rect.bottom(); y += m_gridSize) {
        lines.append(QLineF(rect.left(), y, rect.right(), y));
    }
    
    painter->drawLines(lines.data(), lines.size());
    painter->restore();
}