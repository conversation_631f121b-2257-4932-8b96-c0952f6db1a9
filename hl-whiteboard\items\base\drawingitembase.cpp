#include "drawingitembase.h"
#include <QGraphicsRectItem>
#include <QGraphicsEllipseItem>
#include <QGraphicsLineItem>
#include <QGraphicsPathItem>
#include <QDebug>

DrawingItemBase::DrawingItemBase(WhiteboardTypes::DrawingType type, QGraphicsItem* item)
    : m_type(type)
    , m_antiAliasing(true)
    , m_item(item)
{
    initializeDrawingItem();
}

DrawingItemBase::~DrawingItemBase()
{
}

WhiteboardTypes::DrawingType DrawingItemBase::drawingType() const
{
    return m_type;
}

QJsonObject DrawingItemBase::toJson() const
{
    QJsonObject json;

    // 基本信息
    json["type"] = WhiteboardTypes::drawingTypeToString(m_type);

    // 几何信息（由子类实现）
    json["geometry"] = serializeGeometry();

    // 样式信息 - 直接从图形项获取
    QPen currentPen;
    QBrush currentBrush;

    // 尝试不同的图形项类型
    if (auto* rectItem = qgraphicsitem_cast<QGraphicsRectItem*>(m_item)) {
        currentPen = rectItem->pen();
        currentBrush = rectItem->brush();
    } else if (auto* ellipseItem = qgraphicsitem_cast<QGraphicsEllipseItem*>(m_item)) {
        currentPen = ellipseItem->pen();
        currentBrush = ellipseItem->brush();
    } else if (auto* lineItem = qgraphicsitem_cast<QGraphicsLineItem*>(m_item)) {
        currentPen = lineItem->pen();
        currentBrush = QBrush(Qt::NoBrush); // 线条不支持画刷
    } else if (auto* pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(m_item)) {
        currentPen = pathItem->pen();
        currentBrush = pathItem->brush();
    }

    QJsonObject penObj;
    penObj["color"] = currentPen.color().name();
    penObj["width"] = currentPen.widthF();
    penObj["style"] = static_cast<int>(currentPen.style());
    penObj["capStyle"] = static_cast<int>(currentPen.capStyle());
    penObj["joinStyle"] = static_cast<int>(currentPen.joinStyle());
    json["pen"] = penObj;

    QJsonObject brushObj;
    brushObj["color"] = currentBrush.color().name();
    brushObj["style"] = static_cast<int>(currentBrush.style());
    json["brush"] = brushObj;

    // 变换信息 - 直接从图形项获取
    QJsonObject transformObj;
    transformObj["x"] = m_item->pos().x();
    transformObj["y"] = m_item->pos().y();
    transformObj["rotation"] = m_item->rotation();
    QTransform transform = m_item->transform();
    transformObj["scaleX"] = transform.m11();
    transformObj["scaleY"] = transform.m22();
    json["transform"] = transformObj;

    // 抗锯齿和元数据
    json["antiAliasing"] = m_antiAliasing;
    json["metadata"] = m_metadata;

    return json;
}

bool DrawingItemBase::fromJson(const QJsonObject &json)
{
    try {
        // 验证类型
        QString type = json["type"].toString();
        if (type != WhiteboardTypes::drawingTypeToString(m_type)) {
            qWarning() << "DrawingItemBase::fromJson: Type mismatch. Expected:"
                       << WhiteboardTypes::drawingTypeToString(m_type) << "Got:" << type;
            return false;
        }

        // 几何信息（由子类处理）
        if (!deserializeGeometry(json["geometry"].toObject())) {
            qWarning() << "DrawingItemBase::fromJson: Failed to deserialize geometry";
            return false;
        }

        // 样式信息 - 直接设置到图形项
        QJsonObject penObj = json["pen"].toObject();
        QPen newPen;
        newPen.setColor(QColor(penObj["color"].toString()));
        newPen.setWidthF(penObj["width"].toDouble());
        newPen.setStyle(static_cast<Qt::PenStyle>(penObj["style"].toInt()));
        newPen.setCapStyle(static_cast<Qt::PenCapStyle>(penObj["capStyle"].toInt()));
        newPen.setJoinStyle(static_cast<Qt::PenJoinStyle>(penObj["joinStyle"].toInt()));

        QJsonObject brushObj = json["brush"].toObject();
        QBrush newBrush;
        newBrush.setColor(QColor(brushObj["color"].toString()));
        newBrush.setStyle(static_cast<Qt::BrushStyle>(brushObj["style"].toInt()));

        // 设置样式到不同类型的图形项
        if (auto* rectItem = qgraphicsitem_cast<QGraphicsRectItem*>(m_item)) {
            rectItem->setPen(newPen);
            rectItem->setBrush(newBrush);
        } else if (auto* ellipseItem = qgraphicsitem_cast<QGraphicsEllipseItem*>(m_item)) {
            ellipseItem->setPen(newPen);
            ellipseItem->setBrush(newBrush);
        } else if (auto* lineItem = qgraphicsitem_cast<QGraphicsLineItem*>(m_item)) {
            lineItem->setPen(newPen);
            // 线条不支持画刷
        } else if (auto* pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(m_item)) {
            pathItem->setPen(newPen);
            pathItem->setBrush(newBrush);
        }

        // 变换信息 - 直接设置到图形项
        QJsonObject transformObj = json["transform"].toObject();
        m_item->setPos(QPointF(transformObj["x"].toDouble(), transformObj["y"].toDouble()));
        m_item->setRotation(transformObj["rotation"].toDouble());

        qreal scaleX = transformObj["scaleX"].toDouble(1.0);
        qreal scaleY = transformObj["scaleY"].toDouble(1.0);
        if (scaleX != 1.0 || scaleY != 1.0) {
            m_item->setTransform(QTransform::fromScale(scaleX, scaleY), true);
        }

        // 抗锯齿和元数据
        setAntiAliasing(json["antiAliasing"].toBool(true));
        setMetadata(json["metadata"].toObject());

        return true;
    } catch (...) {
        qWarning() << "DrawingItemBase::fromJson: Exception occurred during deserialization";
        return false;
    }
}

void DrawingItemBase::setAntiAliasing(bool enabled)
{
    m_antiAliasing = enabled;
}

bool DrawingItemBase::antiAliasing() const
{
    return m_antiAliasing;
}

void DrawingItemBase::setMetadata(const QJsonObject &metadata)
{
    m_metadata = metadata;
}

QJsonObject DrawingItemBase::metadata() const
{
    return m_metadata;
}

void DrawingItemBase::setupAntiAliasing(QPainter *painter) const
{
    if (painter && m_antiAliasing) {
        painter->setRenderHint(QPainter::Antialiasing, true);
        painter->setRenderHint(QPainter::SmoothPixmapTransform, true);
    }
}

void DrawingItemBase::initializeDrawingItem()
{
    if (!m_item) {
        return;
    }

    // 设置图形项标志
    m_item->setFlag(QGraphicsItem::ItemIsSelectable, true);
    m_item->setFlag(QGraphicsItem::ItemIsMovable, true);
    m_item->setFlag(QGraphicsItem::ItemSendsGeometryChanges, true);
}
