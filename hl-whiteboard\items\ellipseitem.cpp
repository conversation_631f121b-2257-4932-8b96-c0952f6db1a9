#include "ellipseitem.h"
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QtMath>
#include <QDebug>

EllipseItem::EllipseItem(QGraphicsItem *parent)
    : QGraphicsEllipseItem(parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Ellipse, this)
{
}

EllipseItem::EllipseItem(const QRectF &rect, QGraphicsItem *parent)
    : QGraphicsEllipseItem(rect, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Ellipse, this)
{
}

EllipseItem::EllipseItem(qreal x, qreal y, qreal width, qreal height, QGraphicsItem *parent)
    : QGraphicsEllipseItem(x, y, width, height, parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Ellipse, this)
{
}

EllipseItem::~EllipseItem()
{
}

QJsonObject EllipseItem::serializeGeometry() const
{
    QJsonObject json;

    // 椭圆几何信息
    QRectF currentRect = rect();
    QJsonObject rectObj;
    rectObj["x"] = currentRect.x();
    rectObj["y"] = currentRect.y();
    rectObj["width"] = currentRect.width();
    rectObj["height"] = currentRect.height();
    json["rect"] = rectObj;

    return json;
}

bool EllipseItem::deserializeGeometry(const QJsonObject &json)
{
    try {
        // 读取椭圆几何信息
        QJsonObject rectObj = json["rect"].toObject();
        QRectF newRect(
            rectObj["x"].toDouble(),
            rectObj["y"].toDouble(),
            rectObj["width"].toDouble(),
            rectObj["height"].toDouble()
        );
        setRect(newRect);

        return true;
    } catch (...) {
        qWarning() << "EllipseItem::deserializeGeometry: Exception occurred";
        return false;
    }
}

void EllipseItem::setEllipse(const QRectF &rect)
{
    setRect(rect);
}

void EllipseItem::setEllipse(qreal x, qreal y, qreal width, qreal height)
{
    setEllipse(QRectF(x, y, width, height));
}

QRectF EllipseItem::ellipse() const
{
    return rect();
}

void EllipseItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // 设置抗锯齿
    setupAntiAliasing(painter);

    // 使用默认的椭圆绘制
    QGraphicsEllipseItem::paint(painter, option, widget);
}
